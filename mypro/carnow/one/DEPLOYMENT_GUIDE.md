# CarNow Enhanced Authentication Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Enhanced Authentication system with Supabase integration. All deployment procedures follow Forever Plan principles ensuring production excellence.

---

## **Prerequisites**

### **Required Services**
- ✅ Supabase Project: `lpxtghyvxuenyyisrrro`
- ✅ Go 1.21+ runtime environment
- ✅ PostgreSQL database (via Supabase)
- ✅ Redis cache (optional but recommended)

### **Required Environment Variables**
```bash
# Supabase Configuration
CARNOW_SUPABASE_URL=https://lpxtghyvxuenyyisrrro.supabase.co
CARNOW_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
CARNOW_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
CARNOW_SUPABASE_PROJECT_REF=lpxtghyvxuenyyisrrro

# JWT Configuration
CARNOW_JWT_SECRET=your-secure-jwt-secret-key-here
CARNOW_JWT_EXPIRY=24h

# Google OAuth Configuration
CARNOW_GOOGLE_CLIENT_ID=your-google-client-id
CARNOW_GOOGLE_CLIENT_SECRET=your-google-client-secret

# Server Configuration
CARNOW_SERVER_PORT=8080
CARNOW_SERVER_HOST=0.0.0.0
CARNOW_ENV=production
```

---

## **Deployment Steps**

### **Step 1: Environment Setup**

1. **Create Environment File**
```bash
# Create production environment file
cp .env.example .env.production

# Edit with production values
nano .env.production
```

2. **Validate Configuration**
```bash
# Test configuration loading
go run cmd/main.go --config-check
```

### **Step 2: Build Application**

1. **Build Binary**
```bash
# Build optimized production binary
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o carnow-backend cmd/main.go
```

2. **Verify Build**
```bash
# Test binary execution
./carnow-backend --version
```

### **Step 3: Database Setup**

1. **Verify Supabase Connection**
```bash
# Test Supabase connectivity
curl -H "apikey: $CARNOW_SUPABASE_ANON_KEY" \
     -H "Authorization: Bearer $CARNOW_SUPABASE_ANON_KEY" \
     "$CARNOW_SUPABASE_URL/rest/v1/"
```

2. **Run Database Migrations** (if any)
```bash
# Apply any pending migrations
go run cmd/migrate.go up
```

### **Step 4: Health Check Validation**

1. **Start Application**
```bash
# Start in background
./carnow-backend &
```

2. **Verify Health Endpoints**
```bash
# Check enhanced auth health
curl http://localhost:8080/health/enhanced-auth

# Check Supabase auth health
curl http://localhost:8080/health/supabase-auth

# Check general health
curl http://localhost:8080/health
```

### **Step 5: Authentication Testing**

1. **Test Enhanced Login**
```bash
# Test with real user credentials
curl -X POST http://localhost:8080/api/v1/auth/enhanced/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"real-password"}'
```

2. **Test Protected Endpoints**
```bash
# Test protected profile (use token from login)
curl -H "Authorization: Bearer <access_token>" \
     http://localhost:8080/api/v1/protected/profile
```

---

## **Docker Deployment**

### **Dockerfile**
```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o carnow-backend cmd/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/carnow-backend .
COPY --from=builder /app/.env.production .env

EXPOSE 8080
CMD ["./carnow-backend"]
```

### **Docker Commands**
```bash
# Build image
docker build -t carnow-backend:latest .

# Run container
docker run -d \
  --name carnow-backend \
  -p 8080:8080 \
  --env-file .env.production \
  carnow-backend:latest

# Check logs
docker logs carnow-backend
```

---

## **Kubernetes Deployment**

### **Deployment YAML**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: carnow-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: carnow-backend
  template:
    metadata:
      labels:
        app: carnow-backend
    spec:
      containers:
      - name: carnow-backend
        image: carnow-backend:latest
        ports:
        - containerPort: 8080
        env:
        - name: CARNOW_SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: carnow-secrets
              key: supabase-url
        - name: CARNOW_SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: carnow-secrets
              key: supabase-anon-key
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/enhanced-auth
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

### **Service YAML**
```yaml
apiVersion: v1
kind: Service
metadata:
  name: carnow-backend-service
spec:
  selector:
    app: carnow-backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

---

## **Monitoring and Logging**

### **Health Check Endpoints**
- `/health` - General application health
- `/health/enhanced-auth` - Enhanced auth service health
- `/health/supabase-auth` - Supabase connection health

### **Logging Configuration**
```bash
# Enable structured logging
export CARNOW_LOG_LEVEL=info
export CARNOW_LOG_FORMAT=json

# Log file location
export CARNOW_LOG_FILE=/var/log/carnow/backend.log
```

### **Monitoring Metrics**
- Response time monitoring
- Error rate tracking
- Authentication success/failure rates
- Supabase connection status

---

## **Security Considerations**

### **Environment Variables Security**
- ✅ Never commit secrets to version control
- ✅ Use secure secret management (Kubernetes secrets, etc.)
- ✅ Rotate keys regularly
- ✅ Use different keys for different environments

### **Network Security**
- ✅ Use HTTPS in production
- ✅ Configure proper CORS settings
- ✅ Implement rate limiting
- ✅ Use security headers

### **Forever Plan Compliance**
- ✅ Real data only - no mock data in production
- ✅ Comprehensive error handling
- ✅ Security-first approach
- ✅ Production excellence standards

---

## **Rollback Plan**

### **Quick Rollback Steps**
1. **Stop New Deployment**
```bash
# Stop current deployment
kubectl rollout undo deployment/carnow-backend
```

2. **Verify Previous Version**
```bash
# Check health of previous version
curl http://your-domain/health
```

3. **Database Rollback** (if needed)
```bash
# Rollback database migrations if required
go run cmd/migrate.go down 1
```

### **Rollback Validation**
- ✅ Health checks pass
- ✅ Authentication endpoints working
- ✅ No error rate increase
- ✅ User experience unaffected

---

## **Troubleshooting**

### **Common Issues**

1. **Supabase Connection Failed**
```bash
# Check environment variables
echo $CARNOW_SUPABASE_URL
echo $CARNOW_SUPABASE_ANON_KEY

# Test connectivity
curl -H "apikey: $CARNOW_SUPABASE_ANON_KEY" "$CARNOW_SUPABASE_URL/rest/v1/"
```

2. **Authentication Failures**
```bash
# Check JWT configuration
echo $CARNOW_JWT_SECRET

# Verify token generation
curl -X POST http://localhost:8080/api/v1/auth/enhanced/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password"}'
```

3. **Health Check Failures**
```bash
# Check all health endpoints
curl http://localhost:8080/health
curl http://localhost:8080/health/enhanced-auth
curl http://localhost:8080/health/supabase-auth
```

### **Support Contacts**
- Technical Issues: Check logs and health endpoints
- Configuration Issues: Verify environment variables
- Database Issues: Check Supabase dashboard
- Security Issues: Review authentication logs
