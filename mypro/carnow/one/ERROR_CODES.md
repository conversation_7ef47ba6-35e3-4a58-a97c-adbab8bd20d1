# CarNow Enhanced Authentication Error Codes

## Overview

This document provides a comprehensive reference for all error codes used in the Enhanced Authentication system. All error codes follow Forever Plan principles with clear, actionable error messages.

---

## **Error Code Structure**

### **Format**
```
{
  "success": false,
  "message": "Human-readable error message",
  "error": "Detailed error description",
  "code": "ERROR_CODE_CONSTANT"
}
```

### **HTTP Status Code Mapping**
- **400**: Client errors (validation, bad request)
- **401**: Authentication errors (unauthorized)
- **403**: Authorization errors (forbidden)
- **500**: Server errors (internal, configuration)
- **503**: Service unavailable errors

---

## **Authentication Error Codes (4xx)**

### **AUTH_HEADER_MISSING**
- **HTTP Status**: 401 Unauthorized
- **Description**: Authorization header not provided in request
- **Message**: "Authorization header required"
- **Solution**: Add `Authorization: Bearer <token>` header

**Example:**
```json
{
  "success": false,
  "message": "Authentication required",
  "error": "Authorization header required",
  "code": "AUTH_HEADER_MISSING"
}
```

### **TOKEN_INVALID**
- **HTTP Status**: 401 Unauthorized
- **Description**: JWT token is malformed, corrupted, or invalid
- **Message**: "Invalid token"
- **Solution**: Generate new token via login endpoint

**Example:**
```json
{
  "success": false,
  "message": "Authentication failed",
  "error": "Invalid token format",
  "code": "TOKEN_INVALID"
}
```

### **TOKEN_EXPIRED**
- **HTTP Status**: 401 Unauthorized
- **Description**: JWT token has passed its expiration time
- **Message**: "Token has expired"
- **Solution**: Use refresh token or re-authenticate

**Example:**
```json
{
  "success": false,
  "message": "Token expired",
  "error": "Token has expired, please refresh or login again",
  "code": "TOKEN_EXPIRED"
}
```

### **INVALID_CREDENTIALS**
- **HTTP Status**: 400 Bad Request
- **Description**: Email or password is incorrect
- **Message**: "Invalid credentials"
- **Solution**: Verify email and password are correct

**Example:**
```json
{
  "success": false,
  "message": "Authentication failed",
  "error": "Invalid email or password",
  "code": "INVALID_CREDENTIALS"
}
```

### **VALIDATION_FAILED**
- **HTTP Status**: 400 Bad Request
- **Description**: Request validation failed (missing fields, invalid format)
- **Message**: "Validation failed"
- **Solution**: Check request format and required fields

**Example:**
```json
{
  "success": false,
  "message": "Request validation failed",
  "error": "Email field is required and must be valid",
  "code": "VALIDATION_FAILED"
}
```

### **MOCK_DATA_DETECTED**
- **HTTP Status**: 400 Bad Request
- **Description**: Mock or test data detected (Forever Plan compliance)
- **Message**: "Mock data not allowed"
- **Solution**: Use real user credentials only

**Example:**
```json
{
  "success": false,
  "message": "Mock data detected",
  "error": "Test data not allowed in production environment",
  "code": "MOCK_DATA_DETECTED"
}
```

### **USER_NOT_FOUND**
- **HTTP Status**: 404 Not Found
- **Description**: User does not exist in Supabase database
- **Message**: "User not found"
- **Solution**: Verify user exists or register new user

**Example:**
```json
{
  "success": false,
  "message": "User not found",
  "error": "No user found with provided email",
  "code": "USER_NOT_FOUND"
}
```

### **EMAIL_NOT_CONFIRMED**
- **HTTP Status**: 403 Forbidden
- **Description**: User email has not been confirmed
- **Message**: "Email not confirmed"
- **Solution**: Check email and confirm account

**Example:**
```json
{
  "success": false,
  "message": "Email confirmation required",
  "error": "Please confirm your email before logging in",
  "code": "EMAIL_NOT_CONFIRMED"
}
```

---

## **Server Error Codes (5xx)**

### **CONFIGURATION_ERROR**
- **HTTP Status**: 500 Internal Server Error
- **Description**: Server configuration is invalid or missing
- **Message**: "Configuration error"
- **Solution**: Check environment variables and configuration

**Example:**
```json
{
  "success": false,
  "message": "Service configuration error",
  "error": "Supabase URL configuration is invalid",
  "code": "CONFIGURATION_ERROR"
}
```

### **SERVICE_UNAVAILABLE**
- **HTTP Status**: 503 Service Unavailable
- **Description**: External service (Supabase) is unreachable
- **Message**: "Service temporarily unavailable"
- **Solution**: Check network connectivity and service status

**Example:**
```json
{
  "success": false,
  "message": "Authentication service unavailable",
  "error": "Unable to connect to Supabase authentication service",
  "code": "SERVICE_UNAVAILABLE"
}
```

### **INTERNAL_ERROR**
- **HTTP Status**: 500 Internal Server Error
- **Description**: Unexpected server error occurred
- **Message**: "Internal server error"
- **Solution**: Check server logs for details

**Example:**
```json
{
  "success": false,
  "message": "Internal server error",
  "error": "An unexpected error occurred while processing request",
  "code": "INTERNAL_ERROR"
}
```

### **DATABASE_ERROR**
- **HTTP Status**: 500 Internal Server Error
- **Description**: Database connection or query failed
- **Message**: "Database error"
- **Solution**: Check database connectivity and status

**Example:**
```json
{
  "success": false,
  "message": "Database error",
  "error": "Failed to connect to database",
  "code": "DATABASE_ERROR"
}
```

### **SUPABASE_ERROR**
- **HTTP Status**: 502 Bad Gateway
- **Description**: Supabase API returned an error
- **Message**: "Supabase service error"
- **Solution**: Check Supabase service status

**Example:**
```json
{
  "success": false,
  "message": "Authentication service error",
  "error": "Supabase authentication API returned an error",
  "code": "SUPABASE_ERROR"
}
```

---

## **Rate Limiting Error Codes**

### **RATE_LIMIT_EXCEEDED**
- **HTTP Status**: 429 Too Many Requests
- **Description**: Too many requests from client
- **Message**: "Rate limit exceeded"
- **Solution**: Wait before making more requests

**Example:**
```json
{
  "success": false,
  "message": "Rate limit exceeded",
  "error": "Too many requests, please try again later",
  "code": "RATE_LIMIT_EXCEEDED"
}
```

---

## **Validation Error Codes**

### **INVALID_EMAIL_FORMAT**
- **HTTP Status**: 400 Bad Request
- **Description**: Email format is invalid
- **Message**: "Invalid email format"
- **Solution**: Provide valid email address

### **PASSWORD_TOO_SHORT**
- **HTTP Status**: 400 Bad Request
- **Description**: Password does not meet minimum length
- **Message**: "Password too short"
- **Solution**: Use password with minimum 8 characters

### **MISSING_REQUIRED_FIELD**
- **HTTP Status**: 400 Bad Request
- **Description**: Required field is missing from request
- **Message**: "Missing required field"
- **Solution**: Include all required fields

---

## **Error Handling Best Practices**

### **Client-Side Handling**
```javascript
// Example error handling in JavaScript
try {
  const response = await fetch('/api/v1/auth/enhanced/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
  });
  
  const data = await response.json();
  
  if (!data.success) {
    switch (data.code) {
      case 'INVALID_CREDENTIALS':
        showError('Invalid email or password');
        break;
      case 'MOCK_DATA_DETECTED':
        showError('Please use real credentials');
        break;
      case 'SERVICE_UNAVAILABLE':
        showError('Service temporarily unavailable, please try again');
        break;
      default:
        showError('An error occurred, please try again');
    }
  }
} catch (error) {
  showError('Network error, please check your connection');
}
```

### **Server-Side Logging**
```go
// Example error logging in Go
func (h *EnhancedAuthHandlers) logError(ctx context.Context, err error, operation string) {
    if authErr, ok := err.(*errors.AuthError); ok {
        log.WithFields(log.Fields{
            "operation": operation,
            "error_code": authErr.Code,
            "error_message": authErr.Message,
            "user_ip": getClientIP(ctx),
            "timestamp": time.Now(),
        }).Error("Authentication error occurred")
    }
}
```

---

## **Forever Plan Compliance**

### **Real Data Validation**
All error codes include validation for Forever Plan compliance:

- ✅ **Mock Data Detection**: Automatically detects and rejects test data
- ✅ **Real Data Only**: All authentication must use real Supabase data
- ✅ **Production Excellence**: Clear, actionable error messages
- ✅ **Security First**: No sensitive information in error responses

### **Error Code Categories**
1. **Authentication Errors**: User credential and token issues
2. **Validation Errors**: Request format and data validation
3. **Service Errors**: External service and configuration issues
4. **Forever Plan Errors**: Mock data detection and compliance

### **Monitoring and Alerting**
- Error rates tracked by code
- Critical errors trigger alerts
- Forever Plan violations logged and monitored
- Performance impact of errors measured

---

## **Quick Reference**

### **Common Error Codes**
| Code | Status | Common Cause | Quick Fix |
|------|--------|--------------|-----------|
| `AUTH_HEADER_MISSING` | 401 | No auth header | Add Authorization header |
| `TOKEN_INVALID` | 401 | Bad token | Login again |
| `TOKEN_EXPIRED` | 401 | Expired token | Refresh token |
| `INVALID_CREDENTIALS` | 400 | Wrong password | Check credentials |
| `MOCK_DATA_DETECTED` | 400 | Test data used | Use real data |
| `SERVICE_UNAVAILABLE` | 503 | Supabase down | Check service status |

### **Error Response Format**
All errors follow consistent JSON structure:
```json
{
  "success": false,
  "message": "User-friendly message",
  "error": "Technical details",
  "code": "ERROR_CODE_CONSTANT"
}
```
