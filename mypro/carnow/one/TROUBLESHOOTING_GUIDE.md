# CarNow Enhanced Authentication Troubleshooting Guide

## Overview

This guide provides comprehensive troubleshooting steps for the Enhanced Authentication system. All solutions follow Forever Plan principles with real data only and production excellence.

---

## **Common Issues and Solutions**

### **🔴 Issue 1: Supabase Connection Failed**

**Symptoms:**
- Health check `/health/supabase-auth` returns 500 error
- Authentication endpoints return "SERVICE_UNAVAILABLE"
- Logs show "failed to connect to Supabase"

**Diagnosis:**
```bash
# Check environment variables
echo "URL: $CARNOW_SUPABASE_URL"
echo "Key: ${CARNOW_SUPABASE_ANON_KEY:0:20}..."

# Test direct connection
curl -H "apikey: $CARNOW_SUPABASE_ANON_KEY" \
     -H "Authorization: Bearer $CARNOW_SUPABASE_ANON_KEY" \
     "$CARNOW_SUPABASE_URL/rest/v1/"
```

**Solutions:**
1. **Verify Environment Variables**
```bash
# Check if variables are set
if [ -z "$CARNOW_SUPABASE_URL" ]; then
    echo "❌ CARNOW_SUPABASE_URL not set"
fi

# Verify URL format
if [[ ! "$CARNOW_SUPABASE_URL" =~ ^https?:// ]]; then
    echo "❌ Invalid URL format"
fi
```

2. **Check Supabase Project Status**
- Visit Supabase dashboard
- Verify project is active
- Check API keys are valid

3. **Network Connectivity**
```bash
# Test network connectivity
ping lpxtghyvxuenyyisrrro.supabase.co

# Test HTTPS connectivity
curl -I https://lpxtghyvxuenyyisrrro.supabase.co
```

---

### **🔴 Issue 2: Authentication Failures**

**Symptoms:**
- Login returns "INVALID_CREDENTIALS" for valid users
- Token validation fails
- Users cannot access protected endpoints

**Diagnosis:**
```bash
# Test login with known user
curl -X POST http://localhost:8080/api/v1/auth/enhanced/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"real-password"}' \
     -v

# Check JWT configuration
echo "JWT Secret length: ${#CARNOW_JWT_SECRET}"
```

**Solutions:**
1. **Verify User Exists in Supabase**
```sql
-- Check in Supabase SQL editor
SELECT id, email, email_confirmed_at 
FROM auth.users 
WHERE email = '<EMAIL>';
```

2. **Check JWT Configuration**
```bash
# Verify JWT secret is set and secure
if [ ${#CARNOW_JWT_SECRET} -lt 32 ]; then
    echo "❌ JWT secret too short (minimum 32 characters)"
fi
```

3. **Forever Plan Compliance Check**
```bash
# Test with mock data (should fail)
curl -X POST http://localhost:8080/api/v1/auth/enhanced/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password"}'

# Expected response: MOCK_DATA_DETECTED error
```

---

### **🔴 Issue 3: Health Check Failures**

**Symptoms:**
- `/health/enhanced-auth` returns unhealthy status
- Kubernetes readiness probes failing
- Load balancer removing instances

**Diagnosis:**
```bash
# Check all health endpoints
curl -s http://localhost:8080/health | jq
curl -s http://localhost:8080/health/enhanced-auth | jq
curl -s http://localhost:8080/health/supabase-auth | jq
```

**Solutions:**
1. **Service Dependencies**
```bash
# Check if all required services are running
systemctl status carnow-backend

# Check port availability
netstat -tlnp | grep :8080
```

2. **Configuration Validation**
```bash
# Run configuration check
go run cmd/main.go --config-check

# Verify all required environment variables
env | grep CARNOW_
```

---

### **🔴 Issue 4: Token Validation Errors**

**Symptoms:**
- Protected endpoints return 401 "TOKEN_INVALID"
- Token refresh fails
- Users logged out unexpectedly

**Diagnosis:**
```bash
# Test token validation
TOKEN="your-jwt-token-here"
curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:8080/api/v1/protected/profile \
     -v
```

**Solutions:**
1. **Check Token Format**
```bash
# Verify token structure (should have 3 parts)
echo "$TOKEN" | tr '.' '\n' | wc -l
# Should return 3
```

2. **Verify Token Expiry**
```bash
# Decode JWT payload (base64)
echo "$TOKEN" | cut -d'.' -f2 | base64 -d | jq
```

3. **Check JWT Secret Consistency**
```bash
# Ensure same secret used for signing and validation
echo "Current JWT secret: ${CARNOW_JWT_SECRET:0:10}..."
```

---

### **🔴 Issue 5: Performance Issues**

**Symptoms:**
- Slow response times (>100ms)
- High CPU/memory usage
- Timeout errors

**Diagnosis:**
```bash
# Check response times
time curl http://localhost:8080/health

# Monitor resource usage
top -p $(pgrep carnow-backend)

# Check connection pool
curl http://localhost:8080/debug/vars | jq '.database'
```

**Solutions:**
1. **Database Connection Optimization**
```bash
# Check connection pool settings
echo "Max connections: $CARNOW_DB_MAX_CONNECTIONS"
echo "Idle connections: $CARNOW_DB_MAX_IDLE_CONNECTIONS"
```

2. **Memory Optimization**
```bash
# Check memory usage
ps aux | grep carnow-backend

# Enable garbage collection tuning
export GOGC=100
```

---

## **Error Code Reference**

### **Authentication Errors (4xx)**

| Code | Status | Cause | Solution |
|------|--------|-------|----------|
| `AUTH_HEADER_MISSING` | 401 | No Authorization header | Add `Authorization: Bearer <token>` |
| `TOKEN_INVALID` | 401 | Malformed or invalid token | Generate new token via login |
| `TOKEN_EXPIRED` | 401 | Token past expiry time | Refresh token or re-login |
| `INVALID_CREDENTIALS` | 400 | Wrong email/password | Verify credentials in Supabase |
| `VALIDATION_FAILED` | 400 | Invalid request format | Check request JSON structure |
| `MOCK_DATA_DETECTED` | 400 | Test data in production | Use real user credentials only |

### **Service Errors (5xx)**

| Code | Status | Cause | Solution |
|------|--------|-------|----------|
| `CONFIGURATION_ERROR` | 500 | Missing/invalid config | Check environment variables |
| `SERVICE_UNAVAILABLE` | 503 | Supabase unreachable | Check network and Supabase status |
| `INTERNAL_ERROR` | 500 | Unexpected server error | Check logs for stack trace |
| `DATABASE_ERROR` | 500 | Database connection failed | Verify database connectivity |

---

## **Diagnostic Commands**

### **Quick Health Check**
```bash
#!/bin/bash
# health_check.sh - Quick diagnostic script

echo "=== CarNow Enhanced Auth Health Check ==="

# 1. Check if service is running
if pgrep carnow-backend > /dev/null; then
    echo "✅ Service is running"
else
    echo "❌ Service is not running"
    exit 1
fi

# 2. Check health endpoints
echo "Checking health endpoints..."
curl -s http://localhost:8080/health | jq '.status' || echo "❌ General health failed"
curl -s http://localhost:8080/health/enhanced-auth | jq '.status' || echo "❌ Enhanced auth health failed"
curl -s http://localhost:8080/health/supabase-auth | jq '.status' || echo "❌ Supabase health failed"

# 3. Check environment variables
echo "Checking configuration..."
[ -n "$CARNOW_SUPABASE_URL" ] && echo "✅ Supabase URL set" || echo "❌ Supabase URL missing"
[ -n "$CARNOW_SUPABASE_ANON_KEY" ] && echo "✅ Supabase key set" || echo "❌ Supabase key missing"
[ -n "$CARNOW_JWT_SECRET" ] && echo "✅ JWT secret set" || echo "❌ JWT secret missing"

echo "=== Health check complete ==="
```

### **Performance Monitoring**
```bash
#!/bin/bash
# monitor_performance.sh - Performance monitoring script

echo "=== Performance Monitoring ==="

# Response time test
echo "Testing response times..."
for endpoint in "/health" "/health/enhanced-auth" "/health/supabase-auth"; do
    time_ms=$(curl -w "%{time_total}" -s -o /dev/null http://localhost:8080$endpoint)
    echo "$endpoint: ${time_ms}s"
done

# Resource usage
echo "Resource usage:"
ps aux | grep carnow-backend | grep -v grep

# Connection count
echo "Active connections:"
netstat -an | grep :8080 | wc -l

echo "=== Monitoring complete ==="
```

---

## **Log Analysis**

### **Important Log Patterns**

1. **Authentication Success**
```
INFO: Enhanced login successful for user: <EMAIL>
INFO: Token generated successfully, expires at: 2024-01-01T00:00:00Z
```

2. **Authentication Failure**
```
WARN: Authentication failed for user: <EMAIL>, reason: invalid_credentials
ERROR: Supabase authentication error: 400 Bad Request
```

3. **Forever Plan Compliance**
```
WARN: Mock data detected and rejected: <EMAIL>
INFO: Real data validation passed for user: <EMAIL>
```

4. **Service Health**
```
INFO: Enhanced auth service health check passed
INFO: Supabase connection verified: https://lpxtghyvxuenyyisrrro.supabase.co
```

### **Log Monitoring Commands**
```bash
# Follow logs in real-time
tail -f /var/log/carnow/backend.log

# Filter authentication events
grep "Enhanced.*auth" /var/log/carnow/backend.log

# Check for errors
grep "ERROR\|FATAL" /var/log/carnow/backend.log

# Monitor Forever Plan compliance
grep "Mock data detected" /var/log/carnow/backend.log
```

---

## **Emergency Procedures**

### **Service Recovery**
```bash
# 1. Stop service
systemctl stop carnow-backend

# 2. Check configuration
go run cmd/main.go --config-check

# 3. Restart service
systemctl start carnow-backend

# 4. Verify health
curl http://localhost:8080/health
```

### **Database Connection Recovery**
```bash
# 1. Test Supabase connectivity
curl -H "apikey: $CARNOW_SUPABASE_ANON_KEY" \
     "$CARNOW_SUPABASE_URL/rest/v1/"

# 2. Restart with fresh connections
systemctl restart carnow-backend

# 3. Monitor connection pool
watch "curl -s http://localhost:8080/debug/vars | jq '.database'"
```

---

## **Support and Escalation**

### **Before Escalating**
1. ✅ Run health check script
2. ✅ Check recent logs for errors
3. ✅ Verify environment variables
4. ✅ Test Supabase connectivity
5. ✅ Confirm Forever Plan compliance

### **Information to Provide**
- Error messages and codes
- Health check results
- Recent log entries
- Environment configuration (sanitized)
- Steps to reproduce the issue

### **Forever Plan Compliance Verification**
- ✅ No mock data in production
- ✅ All data from Supabase database
- ✅ Real user authentication only
- ✅ Proper error handling
- ✅ Security best practices
