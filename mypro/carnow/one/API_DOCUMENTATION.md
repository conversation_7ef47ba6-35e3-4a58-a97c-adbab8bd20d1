# CarNow Enhanced Authentication API Documentation

## Overview

This document describes the Enhanced Authentication API endpoints implemented as part of the Supabase JWT migration. All endpoints follow Forever Plan principles with real data only and comprehensive error handling.

---

## **Authentication Endpoints**

### **POST /api/v1/auth/enhanced/login**

Enhanced login endpoint with Supabase integration.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": **********,
    "user": {
      "id": "user-uuid-here",
      "email": "<EMAIL>",
      "email_confirmed": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

**Error Response (400):**
```json
{
  "success": false,
  "message": "Authentication failed",
  "error": "Invalid credentials",
  "code": "INVALID_CREDENTIALS"
}
```

**Forever Plan Compliance:**
- ✅ Detects and rejects mock data (<EMAIL>, etc.)
- ✅ All user data from real Supabase database
- ✅ Comprehensive input validation

---

### **POST /api/v1/auth/enhanced/logout**

Enhanced logout endpoint with token cleanup.

**Headers:**
```
Authorization: Bearer <access_token>
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

**Error Response (401):**
```json
{
  "success": false,
  "message": "Authentication required",
  "error": "Authorization header required",
  "code": "AUTH_HEADER_MISSING"
}
```

---

### **POST /api/v1/auth/enhanced/refresh**

Enhanced token refresh endpoint.

**Request:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": **********
  }
}
```

---

### **GET /api/v1/auth/enhanced/user**

Enhanced user profile retrieval.

**Headers:**
```
Authorization: Bearer <access_token>
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "User retrieved successfully",
  "data": {
    "user": {
      "id": "user-uuid-here",
      "email": "<EMAIL>",
      "email_confirmed": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

---

## **Health Check Endpoints**

### **GET /health/enhanced-auth**

Enhanced authentication service health check.

**Success Response (200):**
```json
{
  "status": "healthy",
  "service": "enhanced-auth",
  "message": "Enhanced authentication service is operational"
}
```

### **GET /health/supabase-auth**

Supabase authentication service health check.

**Success Response (200):**
```json
{
  "status": "healthy",
  "service": "supabase-auth",
  "message": "Supabase authentication service is configured",
  "url": "https://lpxtghyvxuenyyisrrro.supabase.co"
}
```

---

## **Protected Endpoints**

### **GET /api/v1/protected/profile**

Protected user profile endpoint (requires authentication).

**Headers:**
```
Authorization: Bearer <access_token>
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Profile retrieved successfully",
  "data": {
    "profile": {
      "user_id": "user-uuid-here",
      "email": "<EMAIL>",
      "preferences": {},
      "last_login": "2024-01-01T00:00:00Z"
    }
  }
}
```

### **GET /api/v1/protected/dashboard**

Protected dashboard endpoint (requires authentication).

**Headers:**
```
Authorization: Bearer <access_token>
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Dashboard data retrieved successfully",
  "data": {
    "dashboard": {
      "user_stats": {},
      "recent_activity": [],
      "notifications": []
    }
  }
}
```

---

## **Error Codes**

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `AUTH_HEADER_MISSING` | 401 | Authorization header not provided |
| `TOKEN_INVALID` | 401 | Invalid or malformed token |
| `TOKEN_EXPIRED` | 401 | Token has expired |
| `INVALID_CREDENTIALS` | 400 | Invalid email or password |
| `VALIDATION_FAILED` | 400 | Request validation failed |
| `MOCK_DATA_DETECTED` | 400 | Mock data detected (Forever Plan) |
| `CONFIGURATION_ERROR` | 500 | Service configuration error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |
| `INTERNAL_ERROR` | 500 | Internal server error |

---

## **Forever Plan Compliance**

### **Real Data Only Policy**
- ✅ All authentication data from Supabase database
- ✅ Mock data detection and rejection
- ✅ No hardcoded user credentials or test data

### **Security Features**
- ✅ JWT token validation with Supabase
- ✅ Input sanitization and validation
- ✅ Rate limiting and security headers
- ✅ Comprehensive error handling

### **Production Excellence**
- ✅ Health check endpoints for monitoring
- ✅ Structured logging and error tracking
- ✅ Graceful error handling and recovery
- ✅ Performance optimization and caching
