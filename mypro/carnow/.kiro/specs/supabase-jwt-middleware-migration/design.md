# Design Document - Forever Plan Compliant

## Overview

This design outlines the migration from CarNow's current `SimpleAuthMiddleware` to Supabase's JWT middleware system, fully aligned with the **Forever Plan Architecture**. The migration leverages Supa<PERSON> Auth's enterprise-grade security features with AI-enhanced capabilities, including HS256/RS256 encryption, unified token validation, automatic security updates, standardized token formats, comprehensive audit logging, and advanced rate limiting. This transition will significantly improve the application's security posture while reducing maintenance overhead and maintaining **ZERO MOCK DATA TOLERANCE**.

### Forever Plan Compliance
```
Flutter (UI Only) → Go API (AI-Enhanced Production) → Supabase (REAL DATA ONLY + Auth)
                     ↓
Redis Cache + AI Monitoring + Enhanced Security + Material 3 + ML Optimization + ZERO MOCK DATA
```

**Core Principles Enforced:**
- ✅ **REAL DATA ONLY**: All authentication data comes from Supabase database
- ✅ **ZERO MOCK DATA**: Absolute prohibition of hardcoded or fake authentication data
- ✅ **AI-Enhanced Security**: ML-powered threat detection and anomaly prevention
- ✅ **Production Excellence**: Enterprise-grade implementation with 99.99% uptime target
- ✅ **Unified Architecture**: Single source of truth through Supabase Auth

## Architecture

### Current Authentication Architecture Issues (Forever Plan Analysis)
```
Client Request → SimpleAuthMiddleware → Custom JWT Validation → Manual Security Updates
                      ❌                      ❌                        ❌
                 - RSA-256 complexity    - 3 different methods    - Manual patches
                 - Format inconsistencies - Limited error handling - No audit logs
                 - Basic rate limiting   - Security vulnerabilities - Maintenance burden
                 - POTENTIAL MOCK DATA   - No AI enhancement      - No ML optimization
```

### Proposed AI-Enhanced Supabase Authentication Architecture (Forever Plan Compliant)
```
Flutter (UI Only) → AI-Enhanced Supabase JWT Middleware → ML-Powered Validation → Auto Updates
                            ✅                                    ✅                    ✅
                     - HS256/RS256 standard            - AI threat detection    - Auto patches
                     - Standardized format             - ML anomaly prevention  - Comprehensive logs
                     - Advanced rate limiting          - Predictive security    - Zero maintenance
                     - REAL DATA ONLY                  - AI-powered insights    - ML optimization
                     - ZERO MOCK DATA TOLERANCE        - Enhanced monitoring    - Production excellence
```

### Forever Plan Architecture Integration
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter UI    │    │  AI-Enhanced    │    │   Supabase      │    │   Redis Cache   │
│   (Auth UI)     │◄──►│  Go Backend     │◄──►│  (Auth + Data)  │◄──►│  (AI-Optimized) │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │                       │
         ▼                       ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Material 3    │    │  JWT Middleware │    │   Real Data     │    │   AI Monitoring │
│   Design        │◄──►│  (AI-Enhanced)  │◄──►│   (Zero Mock)   │◄──►│  (ML-Powered)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Components and Interfaces

### 1. AI-Enhanced Supabase Client Integration (Forever Plan Compliant)

**Location**: `backend-go/internal/config/supabase.go`

**Purpose**: Initialize and configure Supabase client with AI-enhanced monitoring and zero mock data enforcement

```go
package config

import (
    "context"
    "fmt"
    "os"
    "time"
    
    "github.com/supabase-community/supabase-go"
    "your-app/internal/monitoring"
    "your-app/internal/security"
)

// AI-Enhanced Supabase Configuration (Forever Plan Compliant)
type EnhancedSupabaseConfig struct {
    URL                    string        `json:"url"`
    AnonKey               string        `json:"anon_key"`
    ServiceKey            string        `json:"service_key"`
    JWTSecret             string        `json:"jwt_secret"`
    
    // AI-Enhanced Features
    AIMonitoringEnabled   bool          `json:"ai_monitoring_enabled"`
    MLThreatDetection     bool          `json:"ml_threat_detection"`
    PredictiveScaling     bool          `json:"predictive_scaling"`
    RealDataOnlyMode      bool          `json:"real_data_only_mode"`
    
    // Performance Optimization
    ConnectionTimeout     time.Duration `json:"connection_timeout"`
    MaxRetries           int           `json:"max_retries"`
    CircuitBreakerEnabled bool          `json:"circuit_breaker_enabled"`
    
    // Forever Plan Compliance
    ZeroMockDataTolerance bool          `json:"zero_mock_data_tolerance"`
    ProductionExcellence  bool          `json:"production_excellence"`
}

func NewEnhancedSupabaseConfig() *EnhancedSupabaseConfig {
    return &EnhancedSupabaseConfig{
        URL:        os.Getenv("SUPABASE_URL"),
        AnonKey:    os.Getenv("SUPABASE_ANON_KEY"),
        ServiceKey: os.Getenv("SUPABASE_SERVICE_ROLE_KEY"),
        JWTSecret:  os.Getenv("SUPABASE_JWT_SECRET"),
        
        // AI-Enhanced Defaults (Forever Plan)
        AIMonitoringEnabled:   true,
        MLThreatDetection:     true,
        PredictiveScaling:     true,
        RealDataOnlyMode:      true,
        
        // Performance Defaults
        ConnectionTimeout:     30 * time.Second,
        MaxRetries:           3,
        CircuitBreakerEnabled: true,
        
        // Forever Plan Enforcement
        ZeroMockDataTolerance: true,
        ProductionExcellence:  true,
    }
}

func (c *EnhancedSupabaseConfig) NewAIEnhancedClient() (*supabase.Client, error) {
    // Validate Forever Plan compliance
    if err := c.validateForeverPlanCompliance(); err != nil {
        return nil, fmt.Errorf("Forever Plan compliance failed: %w", err)
    }
    
    // Initialize client with AI-enhanced options
    client, err := supabase.NewClient(c.URL, c.AnonKey, &supabase.ClientOptions{
        Headers: map[string]string{
            "X-CarNow-AI-Enhanced": "true",
            "X-Forever-Plan-Mode":  "production",
            "X-Real-Data-Only":     "true",
        },
    })
    if err != nil {
        return nil, fmt.Errorf("failed to initialize AI-enhanced Supabase client: %w", err)
    }
    
    // Initialize AI monitoring
    if c.AIMonitoringEnabled {
        monitoring.InitializeAIMonitoring(client)
    }
    
    // Initialize ML threat detection
    if c.MLThreatDetection {
        security.InitializeMLThreatDetection(client)
    }
    
    return client, nil
}

func (c *EnhancedSupabaseConfig) validateForeverPlanCompliance() error {
    // Validate required environment variables
    if c.URL == "" || c.AnonKey == "" || c.ServiceKey == "" || c.JWTSecret == "" {
        return fmt.Errorf("missing required Supabase configuration")
    }
    
    // Enforce Zero Mock Data Tolerance
    if !c.ZeroMockDataTolerance {
        return fmt.Errorf("Zero Mock Data Tolerance must be enabled for Forever Plan compliance")
    }
    
    // Enforce Real Data Only Mode
    if !c.RealDataOnlyMode {
        return fmt.Errorf("Real Data Only Mode must be enabled for Forever Plan compliance")
    }
    
    // Enforce Production Excellence
    if !c.ProductionExcellence {
        return fmt.Errorf("Production Excellence must be enabled for Forever Plan compliance")
    }
    
    return nil
}

// AI-Enhanced Health Check (Forever Plan Monitoring)
func (c *EnhancedSupabaseConfig) PerformAIHealthCheck(ctx context.Context, client *supabase.Client) error {
    // Perform comprehensive health check with AI analysis
    healthMetrics := &monitoring.HealthMetrics{
        Timestamp:           time.Now(),
        ConnectionStatus:    "checking",
        AIAnalysisEnabled:   c.AIMonitoringEnabled,
        RealDataValidation:  c.RealDataOnlyMode,
        ForeverPlanMode:     c.ProductionExcellence,
    }
    
    // Test connection with real data query (NO MOCK DATA)
    _, err := client.From("auth.users").Select("count").Execute()
    if err != nil {
        healthMetrics.ConnectionStatus = "failed"
        healthMetrics.ErrorDetails = err.Error()
        return fmt.Errorf("Supabase health check failed: %w", err)
    }
    
    healthMetrics.ConnectionStatus = "healthy"
    
    // AI-powered health analysis
    if c.AIMonitoringEnabled {
        aiAnalysis := monitoring.AnalyzeHealthWithAI(healthMetrics)
        if aiAnalysis.ThreatLevel > 0.7 {
            return fmt.Errorf("AI detected potential health issues: %s", aiAnalysis.Details)
        }
    }
    
    return nil
}
```

### 2. AI-Enhanced Supabase JWT Middleware (Forever Plan Production-Ready)

**Location**: `backend-go/internal/middleware/supabase_auth.go`

**Purpose**: Replace SimpleAuthMiddleware with AI-enhanced Supabase JWT validation following Forever Plan principles

```go
package middleware

import (
    "context"
    "fmt"
    "net/http"
    "strings"
    "time"
    
    "github.com/gin-gonic/gin"
    "github.com/golang-jwt/jwt/v5"
    "github.com/supabase-community/supabase-go"
)

type SupabaseJWTClaims struct {
    Sub         string                 `json:"sub"`
    Aud         string                 `json:"aud"`
    Role        string                 `json:"role"`
    Email       string                 `json:"email"`
    Phone       string                 `json:"phone"`
    AppMetadata map[string]interface{} `json:"app_metadata"`
    UserMetadata map[string]interface{} `json:"user_metadata"`
    jwt.RegisteredClaims
}

// AI-Enhanced Supabase Auth Middleware (Forever Plan Compliant)
type AIEnhancedSupabaseAuthMiddleware struct {
    client              *supabase.Client
    jwtSecret          string
    
    // AI-Enhanced Features
    aiThreatDetector   *security.AIThreatDetector
    mlAnomalyDetector  *security.MLAnomalyDetector
    performanceMonitor *monitoring.AIPerformanceMonitor
    
    // Forever Plan Compliance
    realDataOnlyMode      bool
    zeroMockDataTolerance bool
    productionExcellence  bool
    
    // Advanced Security
    rateLimiter        *security.IntelligentRateLimiter
    circuitBreaker     *resilience.EnhancedCircuitBreaker
    auditLogger        *logging.ComprehensiveAuditLogger
}

func NewAIEnhancedSupabaseAuthMiddleware(
    client *supabase.Client, 
    jwtSecret string,
    config *EnhancedSupabaseConfig,
) *AIEnhancedSupabaseAuthMiddleware {
    middleware := &AIEnhancedSupabaseAuthMiddleware{
        client:    client,
        jwtSecret: jwtSecret,
        
        // Forever Plan Enforcement
        realDataOnlyMode:      config.RealDataOnlyMode,
        zeroMockDataTolerance: config.ZeroMockDataTolerance,
        productionExcellence:  config.ProductionExcellence,
    }
    
    // Initialize AI-Enhanced Components
    if config.MLThreatDetection {
        middleware.aiThreatDetector = security.NewAIThreatDetector()
        middleware.mlAnomalyDetector = security.NewMLAnomalyDetector()
    }
    
    if config.AIMonitoringEnabled {
        middleware.performanceMonitor = monitoring.NewAIPerformanceMonitor()
    }
    
    // Initialize Advanced Security Components
    middleware.rateLimiter = security.NewIntelligentRateLimiter()
    middleware.circuitBreaker = resilience.NewEnhancedCircuitBreaker("supabase_auth")
    middleware.auditLogger = logging.NewComprehensiveAuditLogger()
    
    return middleware
}

// AI-Enhanced JWT Validation (Forever Plan Production-Ready)
func (s *AIEnhancedSupabaseAuthMiddleware) ValidateJWTWithAI() gin.HandlerFunc {
    return func(c *gin.Context) {
        startTime := time.Now()
        
        // Comprehensive audit logging
        auditContext := &logging.AuditContext{
            RequestID:     c.GetHeader("X-Request-ID"),
            ClientIP:      c.ClientIP(),
            UserAgent:     c.GetHeader("User-Agent"),
            Timestamp:     startTime,
            ForeverPlan:   s.productionExcellence,
            RealDataOnly:  s.realDataOnlyMode,
        }
        
        // AI-powered threat detection
        if s.aiThreatDetector != nil {
            threatLevel := s.aiThreatDetector.AnalyzeThreatLevel(c)
            if threatLevel > 0.8 {
                s.auditLogger.LogSecurityThreat(auditContext, threatLevel)
                c.JSON(http.StatusForbidden, gin.H{
                    "error": "AI_THREAT_DETECTED",
                    "message": "Suspicious activity detected",
                    "code": "SECURITY_001",
                })
                c.Abort()
                return
            }
        }
        
        // Intelligent rate limiting
        if !s.rateLimiter.AllowRequest(c.ClientIP()) {
            s.auditLogger.LogRateLimitExceeded(auditContext)
            c.JSON(http.StatusTooManyRequests, gin.H{
                "error": "RATE_LIMIT_EXCEEDED",
                "message": "Too many requests",
                "code": "RATE_001",
            })
            c.Abort()
            return
        }
        
        // Extract and validate authorization header
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            s.auditLogger.LogAuthenticationFailure(auditContext, "missing_header")
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "MISSING_AUTHORIZATION_HEADER",
                "message": "Authorization header is required",
                "code": "AUTH_001",
            })
            c.Abort()
            return
        }

        tokenString := strings.TrimPrefix(authHeader, "Bearer ")
        if tokenString == authHeader {
            s.auditLogger.LogAuthenticationFailure(auditContext, "invalid_format")
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "INVALID_AUTHORIZATION_FORMAT",
                "message": "Authorization header must use Bearer format",
                "code": "AUTH_002",
            })
            c.Abort()
            return
        }

        // Circuit breaker protection
        claims, err := s.circuitBreaker.Execute(func() (interface{}, error) {
            return s.validateSupabaseJWTWithAI(tokenString, auditContext)
        })
        
        if err != nil {
            s.auditLogger.LogAuthenticationFailure(auditContext, err.Error())
            s.handleAIEnhancedAuthError(c, err)
            return
        }
        
        jwtClaims := claims.(*SupabaseJWTClaims)
        
        // ML-powered anomaly detection
        if s.mlAnomalyDetector != nil {
            anomalyScore := s.mlAnomalyDetector.DetectAnomalies(jwtClaims, c)
            if anomalyScore > 0.7 {
                s.auditLogger.LogAnomalyDetected(auditContext, anomalyScore)
                c.JSON(http.StatusUnauthorized, gin.H{
                    "error": "ANOMALY_DETECTED",
                    "message": "Unusual authentication pattern detected",
                    "code": "ML_001",
                })
                c.Abort()
                return
            }
        }

        // Set user context for downstream handlers (REAL DATA ONLY)
        s.setAIEnhancedUserContext(c, jwtClaims, auditContext)
        
        // Performance monitoring
        if s.performanceMonitor != nil {
            duration := time.Since(startTime)
            s.performanceMonitor.RecordAuthenticationLatency(duration)
        }
        
        // Log successful authentication
        s.auditLogger.LogAuthenticationSuccess(auditContext, jwtClaims.Sub)
        
        c.Next()
    }
}

// AI-Enhanced User Context Setting (Forever Plan Compliant)
func (s *AIEnhancedSupabaseAuthMiddleware) setAIEnhancedUserContext(
    c *gin.Context, 
    claims *SupabaseJWTClaims, 
    auditContext *logging.AuditContext,
) {
    // Enforce REAL DATA ONLY - NO MOCK DATA TOLERANCE
    if s.zeroMockDataTolerance {
        if err := s.validateRealDataOnly(claims); err != nil {
            s.auditLogger.LogMockDataViolation(auditContext, err.Error())
            c.JSON(http.StatusForbidden, gin.H{
                "error": "MOCK_DATA_DETECTED",
                "message": "Mock data is not allowed in production",
                "code": "FOREVER_PLAN_001",
            })
            c.Abort()
            return
        }
    }
    
    // Set authenticated user context (REAL DATA FROM SUPABASE)
    c.Set("user_id", claims.Sub)
    c.Set("user_email", claims.Email)
    c.Set("user_phone", claims.Phone)
    c.Set("user_role", claims.Role)
    c.Set("app_metadata", claims.AppMetadata)
    c.Set("user_metadata", claims.UserMetadata)
    
    // Forever Plan compliance indicators
    c.Set("forever_plan_mode", s.productionExcellence)
    c.Set("real_data_only", s.realDataOnlyMode)
    c.Set("ai_enhanced", true)
    c.Set("auth_source", "supabase")
    c.Set("auth_timestamp", time.Now())
}

// Validate Real Data Only (Zero Mock Data Tolerance)
func (s *AIEnhancedSupabaseAuthMiddleware) validateRealDataOnly(claims *SupabaseJWTClaims) error {
    // Check for mock data patterns
    mockPatterns := []string{"mock_", "test_", "fake_", "sample_", "demo_"}
    
    for _, pattern := range mockPatterns {
        if strings.Contains(strings.ToLower(claims.Email), pattern) {
            return fmt.Errorf("mock email detected: %s", claims.Email)
        }
        if strings.Contains(strings.ToLower(claims.Sub), pattern) {
            return fmt.Errorf("mock user ID detected: %s", claims.Sub)
        }
    }
    
    // Validate real Supabase user ID format
    if !isValidSupabaseUserID(claims.Sub) {
        return fmt.Errorf("invalid Supabase user ID format: %s", claims.Sub)
    }
    
    return nil
}

func isValidSupabaseUserID(userID string) bool {
    // Supabase user IDs are UUIDs
    uuidPattern := `^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`
    matched, _ := regexp.MatchString(uuidPattern, userID)
    return matched
}

func (s *SupabaseAuthMiddleware) validateSupabaseJWT(tokenString string) (*SupabaseJWTClaims, error) {
    token, err := jwt.ParseWithClaims(tokenString, &SupabaseJWTClaims{}, func(token *jwt.Token) (interface{}, error) {
        // Supabase supports both HS256 and RS256
        switch token.Method.(type) {
        case *jwt.SigningMethodHMAC:
            return []byte(s.jwtSecret), nil
        case *jwt.SigningMethodRSA:
            // For RS256, we would need to fetch the public key from Supabase
            return s.getSupabasePublicKey()
        default:
            return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
        }
    })

    if err != nil {
        return nil, fmt.Errorf("token parsing failed: %w", err)
    }

    claims, ok := token.Claims.(*SupabaseJWTClaims)
    if !ok || !token.Valid {
        return nil, fmt.Errorf("invalid token claims")
    }

    // Additional Supabase-specific validations
    if err := s.validateSupabaseClaims(claims); err != nil {
        return nil, fmt.Errorf("claims validation failed: %w", err)
    }

    return claims, nil
}

func (s *SupabaseAuthMiddleware) validateSupabaseClaims(claims *SupabaseJWTClaims) error {
    // Validate audience
    if claims.Aud != "authenticated" {
        return fmt.Errorf("invalid audience: %s", claims.Aud)
    }

    // Validate role
    validRoles := []string{"authenticated", "anon", "service_role"}
    roleValid := false
    for _, role := range validRoles {
        if claims.Role == role {
            roleValid = true
            break
        }
    }
    if !roleValid {
        return fmt.Errorf("invalid role: %s", claims.Role)
    }

    // Validate expiration
    if claims.ExpiresAt != nil && claims.ExpiresAt.Before(time.Now()) {
        return fmt.Errorf("token expired")
    }

    return nil
}

func (s *SupabaseAuthMiddleware) getSupabasePublicKey() (interface{}, error) {
    // Implementation to fetch Supabase's public key for RS256 validation
    // This would typically involve calling Supabase's JWKS endpoint
    return nil, fmt.Errorf("RS256 validation not implemented yet")
}
```

### 3. Authentication Service Migration

**Location**: `backend-go/internal/services/auth_service.go`

**Purpose**: Migrate authentication logic to use Supabase Auth APIs

```go
package services

import (
    "context"
    "fmt"
    "time"
    
    "github.com/supabase-community/supabase-go"
    "your-app/internal/models"
)

type SupabaseAuthService struct {
    client *supabase.Client
}

func NewSupabaseAuthService(client *supabase.Client) *SupabaseAuthService {
    return &SupabaseAuthService{
        client: client,
    }
}

// SignInWithEmail authenticates user with email/password
func (s *SupabaseAuthService) SignInWithEmail(email, password string) (*models.AuthResponse, error) {
    resp, err := s.client.Auth.SignInWithEmailPassword(email, password)
    if err != nil {
        return nil, fmt.Errorf("authentication failed: %w", err)
    }

    return &models.AuthResponse{
        AccessToken:  resp.AccessToken,
        RefreshToken: resp.RefreshToken,
        ExpiresIn:    resp.ExpiresIn,
        TokenType:    resp.TokenType,
        User: &models.User{
            ID:    resp.User.ID,
            Email: resp.User.Email,
            Phone: resp.User.Phone,
            Role:  resp.User.Role,
        },
    }, nil
}

// SignInWithOAuth handles OAuth provider authentication
func (s *SupabaseAuthService) SignInWithOAuth(provider string, redirectTo string) (string, error) {
    authURL, err := s.client.Auth.SignInWithProvider(supabase.ProviderGoogle, supabase.SignInOptions{
        RedirectTo: redirectTo,
    })
    if err != nil {
        return "", fmt.Errorf("OAuth URL generation failed: %w", err)
    }
    
    return authURL, nil
}

// RefreshToken refreshes an expired access token
func (s *SupabaseAuthService) RefreshToken(refreshToken string) (*models.AuthResponse, error) {
    resp, err := s.client.Auth.RefreshToken(refreshToken)
    if err != nil {
        return nil, fmt.Errorf("token refresh failed: %w", err)
    }

    return &models.AuthResponse{
        AccessToken:  resp.AccessToken,
        RefreshToken: resp.RefreshToken,
        ExpiresIn:    resp.ExpiresIn,
        TokenType:    resp.TokenType,
    }, nil
}

// SignOut logs out the user and revokes tokens
func (s *SupabaseAuthService) SignOut(accessToken string) error {
    err := s.client.Auth.SignOut(accessToken)
    if err != nil {
        return fmt.Errorf("sign out failed: %w", err)
    }
    return nil
}

// GetUser retrieves user information from token
func (s *SupabaseAuthService) GetUser(accessToken string) (*models.User, error) {
    user, err := s.client.Auth.GetUser(accessToken)
    if err != nil {
        return nil, fmt.Errorf("get user failed: %w", err)
    }

    return &models.User{
        ID:           user.ID,
        Email:        user.Email,
        Phone:        user.Phone,
        Role:         user.Role,
        AppMetadata:  user.AppMetadata,
        UserMetadata: user.UserMetadata,
        CreatedAt:    user.CreatedAt,
        UpdatedAt:    user.UpdatedAt,
    }, nil
}
```

### 4. Migration Handler

**Location**: `backend-go/internal/handlers/migration.go`

**Purpose**: Handle backward compatibility during migration period

```go
package handlers

import (
    "net/http"
    "strings"
    "time"
    
    "github.com/gin-gonic/gin"
)

type MigrationHandler struct {
    legacyAuth    *LegacyAuthService
    supabaseAuth  *SupabaseAuthService
    migrationEnd  time.Time
}

func NewMigrationHandler(legacy *LegacyAuthService, supabase *SupabaseAuthService) *MigrationHandler {
    // Set migration end date (e.g., 30 days from deployment)
    migrationEnd := time.Now().Add(30 * 24 * time.Hour)
    
    return &MigrationHandler{
        legacyAuth:   legacy,
        supabaseAuth: supabase,
        migrationEnd: migrationEnd,
    }
}

func (m *MigrationHandler) HybridAuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "missing authorization header"})
            c.Abort()
            return
        }

        tokenString := strings.TrimPrefix(authHeader, "Bearer ")
        
        // Try Supabase validation first
        if user, err := m.supabaseAuth.ValidateToken(tokenString); err == nil {
            m.setUserContext(c, user, "supabase")
            c.Next()
            return
        }

        // Fallback to legacy validation if migration period is active
        if time.Now().Before(m.migrationEnd) {
            if user, err := m.legacyAuth.ValidateToken(tokenString); err == nil {
                m.setUserContext(c, user, "legacy")
                // Log for migration tracking
                m.logLegacyTokenUsage(user.ID, tokenString)
                c.Next()
                return
            }
        }

        c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid token"})
        c.Abort()
    }
}

func (m *MigrationHandler) setUserContext(c *gin.Context, user *models.User, source string) {
    c.Set("user_id", user.ID)
    c.Set("user_email", user.Email)
    c.Set("user_role", user.Role)
    c.Set("auth_source", source)
}

func (m *MigrationHandler) logLegacyTokenUsage(userID, token string) {
    // Log legacy token usage for migration tracking
    log.Printf("Legacy token used by user %s, token: %s...", userID, token[:10])
}
```

## Data Models

### Enhanced User Model for Supabase Integration

```go
package models

import (
    "time"
)

type User struct {
    ID           string                 `json:"id" gorm:"primaryKey"`
    Email        string                 `json:"email" gorm:"uniqueIndex"`
    Phone        string                 `json:"phone"`
    Role         string                 `json:"role"`
    AppMetadata  map[string]interface{} `json:"app_metadata" gorm:"type:jsonb"`
    UserMetadata map[string]interface{} `json:"user_metadata" gorm:"type:jsonb"`
    CreatedAt    time.Time              `json:"created_at"`
    UpdatedAt    time.Time              `json:"updated_at"`
    
    // Supabase-specific fields
    Aud              string    `json:"aud"`
    ConfirmationSentAt *time.Time `json:"confirmation_sent_at"`
    RecoverySentAt     *time.Time `json:"recovery_sent_at"`
    EmailConfirmedAt   *time.Time `json:"email_confirmed_at"`
    PhoneConfirmedAt   *time.Time `json:"phone_confirmed_at"`
    LastSignInAt       *time.Time `json:"last_sign_in_at"`
}

type AuthResponse struct {
    AccessToken  string `json:"access_token"`
    RefreshToken string `json:"refresh_token"`
    ExpiresIn    int    `json:"expires_in"`
    TokenType    string `json:"token_type"`
    User         *User  `json:"user,omitempty"`
}

type SupabaseAuthConfig struct {
    URL        string `json:"url"`
    AnonKey    string `json:"anon_key"`
    ServiceKey string `json:"service_key"`
    JWTSecret  string `json:"jwt_secret"`
}
```

### JWT Token Structure (Supabase Standard)

```json
{
  "aud": "authenticated",
  "exp": **********,
  "iat": **********,
  "iss": "https://your-project.supabase.co/auth/v1",
  "sub": "11111111-**************-************",
  "email": "<EMAIL>",
  "phone": "+**********",
  "app_metadata": {
    "provider": "google",
    "providers": ["google"]
  },
  "user_metadata": {
    "avatar_url": "https://example.com/avatar.jpg",
    "full_name": "John Doe"
  },
  "role": "authenticated",
  "aal": "aal1",
  "amr": [
    {
      "method": "oauth",
      "timestamp": **********
    }
  ],
  "session_id": "session-uuid"
}
```

## Error Handling

### Supabase-Specific Error Handling

```go
package middleware

import (
    "errors"
    "net/http"
    "strings"
    
    "github.com/gin-gonic/gin"
)

type SupabaseError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

func (s *SupabaseAuthMiddleware) handleAuthError(c *gin.Context, err error) {
    var supabaseErr SupabaseError
    
    switch {
    case strings.Contains(err.Error(), "token is expired"):
        supabaseErr = SupabaseError{
            Code:    "TOKEN_EXPIRED",
            Message: "Access token has expired",
            Details: "Please refresh your token or sign in again",
        }
        c.JSON(http.StatusUnauthorized, supabaseErr)
        
    case strings.Contains(err.Error(), "invalid signature"):
        supabaseErr = SupabaseError{
            Code:    "INVALID_SIGNATURE",
            Message: "Token signature is invalid",
            Details: "Token may have been tampered with",
        }
        c.JSON(http.StatusUnauthorized, supabaseErr)
        
    case strings.Contains(err.Error(), "invalid audience"):
        supabaseErr = SupabaseError{
            Code:    "INVALID_AUDIENCE",
            Message: "Token audience is invalid",
            Details: "Token was not issued for this application",
        }
        c.JSON(http.StatusUnauthorized, supabaseErr)
        
    case strings.Contains(err.Error(), "user not found"):
        supabaseErr = SupabaseError{
            Code:    "USER_NOT_FOUND",
            Message: "User does not exist",
            Details: "The user associated with this token was not found",
        }
        c.JSON(http.StatusUnauthorized, supabaseErr)
        
    default:
        supabaseErr = SupabaseError{
            Code:    "AUTHENTICATION_FAILED",
            Message: "Authentication failed",
            Details: "Please check your credentials and try again",
        }
        c.JSON(http.StatusUnauthorized, supabaseErr)
    }
    
    c.Abort()
}

// Rate limiting error handling
func (s *SupabaseAuthMiddleware) handleRateLimitError(c *gin.Context) {
    c.JSON(http.StatusTooManyRequests, SupabaseError{
        Code:    "RATE_LIMIT_EXCEEDED",
        Message: "Too many requests",
        Details: "Please wait before making another request",
    })
    c.Abort()
}
```

## Testing Strategy

### 1. Unit Tests

```go
package middleware_test

import (
    "testing"
    "time"
    
    "github.com/golang-jwt/jwt/v5"
    "github.com/stretchr/testify/assert"
)

func TestSupabaseJWTValidation(t *testing.T) {
    tests := []struct {
        name        string
        token       string
        expectError bool
        errorType   string
    }{
        {
            name:        "Valid Supabase Token",
            token:       generateValidSupabaseToken(),
            expectError: false,
        },
        {
            name:        "Expired Token",
            token:       generateExpiredToken(),
            expectError: true,
            errorType:   "TOKEN_EXPIRED",
        },
        {
            name:        "Invalid Signature",
            token:       generateInvalidSignatureToken(),
            expectError: true,
            errorType:   "INVALID_SIGNATURE",
        },
        {
            name:        "Invalid Audience",
            token:       generateInvalidAudienceToken(),
            expectError: true,
            errorType:   "INVALID_AUDIENCE",
        },
    }

    middleware := NewSupabaseAuthMiddleware(mockClient, "test-secret")

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            claims, err := middleware.validateSupabaseJWT(tt.token)
            
            if tt.expectError {
                assert.Error(t, err)
                assert.Contains(t, err.Error(), tt.errorType)
                assert.Nil(t, claims)
            } else {
                assert.NoError(t, err)
                assert.NotNil(t, claims)
                assert.Equal(t, "authenticated", claims.Aud)
            }
        })
    }
}
```

### 2. Integration Tests

```go
func TestSupabaseAuthIntegration(t *testing.T) {
    // Test complete authentication flow
    client := setupTestSupabaseClient()
    service := NewSupabaseAuthService(client)
    
    // Test sign in
    authResp, err := service.SignInWithEmail("<EMAIL>", "password")
    assert.NoError(t, err)
    assert.NotEmpty(t, authResp.AccessToken)
    
    // Test token validation
    user, err := service.GetUser(authResp.AccessToken)
    assert.NoError(t, err)
    assert.Equal(t, "<EMAIL>", user.Email)
    
    // Test token refresh
    newAuth, err := service.RefreshToken(authResp.RefreshToken)
    assert.NoError(t, err)
    assert.NotEqual(t, authResp.AccessToken, newAuth.AccessToken)
}
```

### 3. Migration Tests

```go
func TestMigrationCompatibility(t *testing.T) {
    handler := NewMigrationHandler(legacyAuth, supabaseAuth)
    
    // Test legacy token still works during migration period
    legacyToken := generateLegacyToken()
    user, err := handler.ValidateToken(legacyToken)
    assert.NoError(t, err)
    assert.NotNil(t, user)
    
    // Test Supabase token works
    supabaseToken := generateSupabaseToken()
    user, err = handler.ValidateToken(supabaseToken)
    assert.NoError(t, err)
    assert.NotNil(t, user)
}
```

## Forever Plan Compliance & Security

### 1. Zero Mock Data Enforcement (ABSOLUTE REQUIREMENT)
```go
// ✅ CORRECT: Real data validation
func (s *AIEnhancedSupabaseAuthMiddleware) validateRealDataOnly(claims *SupabaseJWTClaims) error {
    // Detect and reject any mock data patterns
    mockPatterns := []string{"mock_", "test_", "fake_", "sample_", "demo_"}
    
    for _, pattern := range mockPatterns {
        if strings.Contains(strings.ToLower(claims.Email), pattern) {
            return fmt.Errorf("FOREVER_PLAN_VIOLATION: mock email detected")
        }
    }
    
    // Validate real Supabase user data
    if !isValidSupabaseUserID(claims.Sub) {
        return fmt.Errorf("FOREVER_PLAN_VIOLATION: invalid user ID format")
    }
    
    return nil
}

// ❌ FORBIDDEN: Mock data patterns (ZERO TOLERANCE)
// return &SupabaseJWTClaims{Sub: "mock_user_123"} // ABSOLUTELY FORBIDDEN!
// return &SupabaseJWTClaims{Email: "<EMAIL>"} // HARDCODED DATA FORBIDDEN!
```

### 2. AI-Enhanced Security (Production Excellence)
- **ML Threat Detection**: AI-powered analysis of authentication patterns
- **Predictive Anomaly Detection**: Machine learning-based suspicious activity identification
- **Intelligent Rate Limiting**: Adaptive throttling based on behavioral analysis
- **Real-time Security Monitoring**: Continuous AI-powered security assessment
- **Automated Threat Response**: ML-driven security incident handling

### 3. Advanced Token Security
- **JWT Secret Management**: Secure environment variable storage with rotation
- **Token Rotation**: Automatic refresh with predictive expiration handling
- **Secure Storage**: HTTP-only cookies with AI-optimized security headers
- **Token Validation**: Multi-layer verification with ML-powered fraud detection

### 4. Enterprise Rate Limiting
- **Supabase Built-in**: Leverage advanced infrastructure-level protection
- **AI-Enhanced Limits**: Machine learning-powered adaptive rate limiting
- **DDoS Protection**: Multi-tier defense with predictive threat mitigation
- **Behavioral Analysis**: AI-powered user behavior pattern recognition

### 5. Comprehensive Audit Logging
- **Authentication Events**: Complete logging with AI-powered analysis
- **Token Usage**: Detailed tracking with ML-based pattern recognition
- **Security Incidents**: Real-time monitoring with automated alerting
- **Compliance Reporting**: Forever Plan adherence verification
- **Performance Metrics**: AI-enhanced monitoring and optimization

### 6. Forever Plan Architecture Compliance
```yaml
Forever Plan Compliance Checklist:
  ✅ Real Data Only: All authentication data from Supabase database
  ✅ Zero Mock Data: Absolute prohibition of fake or test data
  ✅ AI Enhancement: Machine learning-powered security and performance
  ✅ Production Excellence: Enterprise-grade implementation standards
  ✅ Unified Architecture: Single source of truth through Supabase
  ✅ Material 3 Integration: Design system compliance for auth UI
  ✅ Monitoring Excellence: Comprehensive observability and alerting
  ✅ Security First: Multi-layer protection with AI enhancement
```

## Implementation Priority

### Phase 1: Foundation (Week 1-2)
1. **High Priority**: Set up Supabase client and configuration
2. **High Priority**: Implement basic Supabase JWT middleware
3. **High Priority**: Create migration handler for backward compatibility

### Phase 2: Core Migration (Week 3-4)
1. **High Priority**: Migrate authentication endpoints to Supabase
2. **Medium Priority**: Update user models and database schema
3. **Medium Priority**: Implement comprehensive error handling

### Phase 3: Advanced Features (Week 5-6)
1. **Medium Priority**: Add advanced rate limiting configuration
2. **Medium Priority**: Implement comprehensive audit logging
3. **Low Priority**: Add monitoring and alerting for authentication events

### Phase 4: Cleanup (Week 7-8)
1. **Low Priority**: Remove legacy authentication code
2. **Low Priority**: Optimize performance and monitoring
3. **Low Priority**: Documentation and training updates

## Monitoring and Observability

### Authentication Metrics
- Token validation success/failure rates
- Authentication method usage (email, OAuth, etc.)
- Token refresh frequency
- Rate limiting trigger events

### Performance Metrics
- Authentication response times
- Token validation latency
- Database query performance
- External API call duration

### Security Metrics
- Failed authentication attempts
- Suspicious token usage patterns
- Rate limiting effectiveness
- Security incident detection

## Forever Plan Success Metrics

### AI-Enhanced Performance Targets
```yaml
Production Excellence Targets (Forever Plan Compliant):
  Authentication Performance:
    - JWT validation: < 25ms (AI-optimized)
    - Token refresh: < 50ms (ML-enhanced)
    - User context setting: < 5ms (optimized)
    - Error handling: < 10ms (intelligent)

  Security Metrics:
    - Threat detection accuracy: > 99% (AI-powered)
    - Anomaly detection rate: > 95% (ML-based)
    - False positive rate: < 1% (optimized)
    - Security incident response: < 30s (automated)

  Forever Plan Compliance:
    - Real data validation: 100% (zero tolerance)
    - Mock data detection: 100% (comprehensive)
    - Architecture adherence: 100% (validated)
    - Production excellence: 99.99% uptime
```

### Monitoring and Alerting
```go
// AI-Enhanced Monitoring Endpoints (Forever Plan Approved)
router.GET("/health/auth", api.AIEnhancedAuthHealth)
router.GET("/metrics/auth/ai", api.AuthAIMetrics)
router.GET("/security/threats/realtime", api.RealTimeThreatAnalysis)
router.GET("/compliance/forever-plan", api.ForeverPlanCompliance)
router.GET("/performance/auth/ml", api.MLAuthPerformance)
```

## Forever Plan Commitment

```
🚀 ENHANCED FOREVER PLAN PROMISE FOR SUPABASE JWT MIGRATION:

"نحن لا نهاجر فقط - نحن نطور نظام مصادقة مدعوم بالذكاء الاصطناعي"
"We don't just migrate - we evolve to an AI-powered authentication system"

✅ REAL DATA ONLY: All authentication data from Supabase database
✅ ZERO MOCK DATA: Absolute prohibition with AI-powered detection
✅ AI ENHANCEMENT: Machine learning-powered security and performance
✅ PRODUCTION EXCELLENCE: Enterprise-grade with 99.99% uptime
✅ UNIFIED ARCHITECTURE: Single source of truth through Supabase
✅ MATERIAL 3 INTEGRATION: Seamless design system compliance
✅ COMPREHENSIVE MONITORING: AI-enhanced observability and alerting

🎯 GOAL: Transform authentication into an AI-powered, production-ready system
🎯 GOAL: Achieve zero security incidents with predictive threat prevention
🎯 GOAL: Maintain Forever Plan compliance with continuous validation
```

This design provides a comprehensive, AI-enhanced roadmap for migrating from SimpleAuthMiddleware to Supabase's JWT middleware system, ensuring improved security, reduced maintenance overhead, enhanced functionality, and complete Forever Plan compliance with **ZERO MOCK DATA TOLERANCE**.