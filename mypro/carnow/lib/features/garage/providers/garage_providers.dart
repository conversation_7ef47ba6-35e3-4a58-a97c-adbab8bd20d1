// ============================================================================
// GARAGE PROVIDERS - Forever Plan Architecture
// ============================================================================
// 
// This file contains all Riverpod providers for the garage feature.
// Following Forever Plan principles:
// - REAL DATA ONLY from Go API
// - NO MOCK DATA tolerance
// - Material 3 Design System
// - Clean Architecture
// - Performance optimized
//
// ============================================================================

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:carnow/core/networking/simple_api_client.dart';
import 'package:carnow/features/garage/models/garage_models.dart';
import 'package:carnow/features/garage/repositories/garage_repository.dart';
import 'package:carnow/features/garage/services/garage_service.dart';

part 'garage_providers.g.dart';

// ============================================================================
// REPOSITORY PROVIDERS
// ============================================================================

@riverpod
GarageRepository garageRepository(Ref ref) {
  // Use read instead of watch to avoid circular dependency with auth system
  final apiClient = ref.read(simpleApiClientProvider);
  return GarageRepository(apiClient);
}

// ============================================================================
// SERVICE PROVIDERS
// ============================================================================

@riverpod
GarageService garageService(Ref ref) {
  // Use read instead of watch to avoid circular dependency with auth system
  final repository = ref.read(garageRepositoryProvider);
  return GarageService(repository);
}

// ============================================================================
// VEHICLE REFERENCE DATA PROVIDERS
// ============================================================================

@riverpod
Future<List<VehicleMake>> vehicleMakes(Ref ref, {String? search}) async {
  // Use read instead of watch to avoid circular dependency with auth system
  final service = ref.read(garageServiceProvider);
  return await service.getVehicleMakes(search: search);
}

@riverpod
Future<List<VehicleModel>> vehicleModels(Ref ref, int makeId, {String? search}) async {
  // Use read instead of watch to avoid circular dependency with auth system
  final service = ref.read(garageServiceProvider);
  return await service.getVehicleModels(makeId, search: search);
}

@riverpod
Future<List<VehicleYear>> vehicleYears(Ref ref, int modelId) async {
  // Use read instead of watch to avoid circular dependency with auth system
  final service = ref.read(garageServiceProvider);
  return await service.getVehicleYears(modelId);
}

@riverpod
Future<List<VehicleTrim>> vehicleTrims(Ref ref, int modelId) async {
  // Use read instead of watch to avoid circular dependency with auth system
  final service = ref.read(garageServiceProvider);
  return await service.getVehicleTrims(modelId);
}

@riverpod
Future<List<VehicleEngine>> vehicleEngines(Ref ref, int modelId) async {
  // Use read instead of watch to avoid circular dependency with auth system
  final service = ref.read(garageServiceProvider);
  return await service.getVehicleEngines(modelId);
}

// ============================================================================
// USER VEHICLE PROVIDERS
// ============================================================================

@riverpod
Future<List<UserVehicle>> myVehicles(Ref ref) async {
  // Use read instead of watch to avoid circular dependency with auth system
  final service = ref.read(garageServiceProvider);
  return await service.getMyVehicles();
}

@riverpod
Future<UserVehicle?> primaryVehicle(Ref ref) async {
  // Use read instead of watch to avoid circular dependency with auth system
  final vehicles = await ref.read(myVehiclesProvider.future);
  return vehicles.where((v) => v.isPrimary).firstOrNull;
}

// ============================================================================
// VEHICLE SELECTION STATE PROVIDERS
// ============================================================================

@riverpod
class VehicleSelectionState extends _$VehicleSelectionState {
  @override
  VehicleSelection build() {
    return const VehicleSelection();
  }

  void selectMake(VehicleMake make) {
    state = state.copyWith(
      selectedMake: make,
      selectedModel: null,
      selectedYear: null,
      selectedTrim: null,
      selectedEngine: null,
    );
  }

  void selectModel(VehicleModel model) {
    state = state.copyWith(
      selectedModel: model,
      selectedYear: null,
      selectedTrim: null,
      selectedEngine: null,
    );
  }

  void selectYear(VehicleYear year) {
    state = state.copyWith(selectedYear: year);
  }

  void selectTrim(VehicleTrim trim) {
    state = state.copyWith(selectedTrim: trim);
  }

  void selectEngine(VehicleEngine engine) {
    state = state.copyWith(selectedEngine: engine);
  }

  void reset() {
    state = const VehicleSelection();
  }

  bool get canCreateVehicle {
    return state.selectedMake != null && state.selectedModel != null;
  }
}

// ============================================================================
// VEHICLE SELECTION MODEL
// ============================================================================

class VehicleSelection {
  final VehicleMake? selectedMake;
  final VehicleModel? selectedModel;
  final VehicleYear? selectedYear;
  final VehicleTrim? selectedTrim;
  final VehicleEngine? selectedEngine;

  const VehicleSelection({
    this.selectedMake,
    this.selectedModel,
    this.selectedYear,
    this.selectedTrim,
    this.selectedEngine,
  });

  VehicleSelection copyWith({
    VehicleMake? selectedMake,
    VehicleModel? selectedModel,
    VehicleYear? selectedYear,
    VehicleTrim? selectedTrim,
    VehicleEngine? selectedEngine,
  }) {
    return VehicleSelection(
      selectedMake: selectedMake ?? this.selectedMake,
      selectedModel: selectedModel ?? this.selectedModel,
      selectedYear: selectedYear ?? this.selectedYear,
      selectedTrim: selectedTrim ?? this.selectedTrim,
      selectedEngine: selectedEngine ?? this.selectedEngine,
    );
  }
}

// ============================================================================
// GARAGE ACTIONS PROVIDER
// ============================================================================

@riverpod
class GarageActions extends _$GarageActions {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  Future<void> createVehicle({
    required int makeRefId,
    required int modelRefId,
    int? yearRefId,
    int? trimRefId,
    int? engineRefId,
    String? color,
    String? vin,
    String? licensePlate,
    int? mileage,
    bool isPrimary = false,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      final service = ref.read(garageServiceProvider);
      await service.createUserVehicle(
        makeRefId: makeRefId,
        modelRefId: modelRefId,
        yearRefId: yearRefId,
        trimRefId: trimRefId,
        engineRefId: engineRefId,
        color: color,
        vin: vin,
        licensePlate: licensePlate,
        mileage: mileage,
        isPrimary: isPrimary,
      );
      
      // Refresh vehicles list
      ref.invalidate(myVehiclesProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateVehicle({
    required int vehicleId,
    String? color,
    String? vin,
    String? licensePlate,
    int? mileage,
    bool? isPrimary,
    List<String>? imageUrls,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      final service = ref.read(garageServiceProvider);
      await service.updateUserVehicle(
        vehicleId: vehicleId,
        color: color,
        vin: vin,
        licensePlate: licensePlate,
        mileage: mileage,
        isPrimary: isPrimary,
        imageUrls: imageUrls,
      );
      
      // Refresh vehicles list
      ref.invalidate(myVehiclesProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteVehicle(int vehicleId) async {
    state = const AsyncValue.loading();
    
    try {
      final service = ref.read(garageServiceProvider);
      await service.deleteUserVehicle(vehicleId);
      
      // Refresh vehicles list
      ref.invalidate(myVehiclesProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> setPrimaryVehicle(int vehicleId) async {
    state = const AsyncValue.loading();
    
    try {
      final service = ref.read(garageServiceProvider);
      await service.updateUserVehicle(
        vehicleId: vehicleId,
        isPrimary: true,
      );
      
      // Refresh vehicles list
      ref.invalidate(myVehiclesProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
