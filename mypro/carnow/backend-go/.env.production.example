# CarNow Backend - Production Environment Variables
# Copy this file to .env.production and update with real values

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
CARNOW_ENV=production
CARNOW_SERVER_HOST=0.0.0.0
CARNOW_SERVER_PORT=8080
CARNOW_DEBUG=false

# =============================================================================
# SUPABASE CONFIGURATION (REQUIRED)
# =============================================================================
# Supabase Project URL - Replace with your actual project URL
CARNOW_SUPABASE_URL=https://lpxtghyvxuenyyisrrro.supabase.co

# Supabase Anonymous Key - Get from Supabase Dashboard > Settings > API
CARNOW_SUPABASE_ANON_KEY=your-supabase-anon-key-here

# Supabase Service Role Key - Get from Supabase Dashboard > Settings > API
CARNOW_SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here

# Supabase Project Reference - Your project ID
CARNOW_SUPABASE_PROJECT_REF=lpxtghyvxuenyyisrrro

# =============================================================================
# JWT CONFIGURATION (REQUIRED)
# =============================================================================
# JWT Secret - Use a strong, random secret (minimum 32 characters)
CARNOW_JWT_SECRET=your-super-secure-jwt-secret-key-minimum-32-characters

# JWT Token Expiry Duration
CARNOW_JWT_EXPIRY=24h

# =============================================================================
# GOOGLE OAUTH CONFIGURATION (REQUIRED)
# =============================================================================
# Google OAuth Client ID - Get from Google Cloud Console
CARNOW_GOOGLE_CLIENT_ID=your-google-oauth-client-id

# Google OAuth Client Secret - Get from Google Cloud Console
CARNOW_GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret

# =============================================================================
# DATABASE CONFIGURATION (OPTIONAL - Uses Supabase by default)
# =============================================================================
# Database connection settings (if using separate database)
# CARNOW_DB_HOST=localhost
# CARNOW_DB_PORT=5432
# CARNOW_DB_NAME=carnow
# CARNOW_DB_USER=carnow_user
# CARNOW_DB_PASSWORD=secure_password
# CARNOW_DB_SSL_MODE=require

# Connection pool settings
CARNOW_DB_MAX_CONNECTIONS=25
CARNOW_DB_MAX_IDLE_CONNECTIONS=5
CARNOW_DB_CONNECTION_MAX_LIFETIME=300s

# =============================================================================
# REDIS CONFIGURATION (OPTIONAL)
# =============================================================================
# Redis connection for caching and sessions
# CARNOW_REDIS_HOST=localhost
# CARNOW_REDIS_PORT=6379
# CARNOW_REDIS_PASSWORD=
# CARNOW_REDIS_DB=0

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log level: debug, info, warn, error
CARNOW_LOG_LEVEL=info

# Log format: json, text
CARNOW_LOG_FORMAT=json

# Log file path (optional - logs to stdout if not set)
# CARNOW_LOG_FILE=/var/log/carnow/backend.log

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# CORS settings
CARNOW_CORS_ALLOWED_ORIGINS=https://carnow.app,https://www.carnow.app
CARNOW_CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CARNOW_CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With

# Rate limiting
CARNOW_RATE_LIMIT_REQUESTS_PER_MINUTE=60
CARNOW_RATE_LIMIT_BURST=10

# Security headers
CARNOW_SECURITY_HEADERS_ENABLED=true

# =============================================================================
# MONITORING AND HEALTH CHECKS
# =============================================================================
# Health check intervals
CARNOW_HEALTH_CHECK_INTERVAL=30s
CARNOW_HEALTH_CHECK_TIMEOUT=10s

# Metrics collection
CARNOW_METRICS_ENABLED=true
CARNOW_METRICS_PATH=/metrics

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Request timeout
CARNOW_REQUEST_TIMEOUT=30s

# Read/Write timeouts
CARNOW_READ_TIMEOUT=10s
CARNOW_WRITE_TIMEOUT=10s

# Idle timeout
CARNOW_IDLE_TIMEOUT=60s

# =============================================================================
# FOREVER PLAN COMPLIANCE
# =============================================================================
# Mock data detection (always enabled in production)
CARNOW_MOCK_DATA_DETECTION=true

# Real data validation (always enabled in production)
CARNOW_REAL_DATA_VALIDATION=true

# Production excellence mode
CARNOW_PRODUCTION_EXCELLENCE=true

# =============================================================================
# DEPLOYMENT SPECIFIC
# =============================================================================
# Application version (set during deployment)
CARNOW_VERSION=1.0.0

# Build timestamp (set during deployment)
CARNOW_BUILD_TIME=2024-01-01T00:00:00Z

# Git commit hash (set during deployment)
CARNOW_GIT_COMMIT=abc123def456

# Deployment environment
CARNOW_DEPLOYMENT_ENV=production

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Email service configuration (if needed)
# CARNOW_EMAIL_PROVIDER=sendgrid
# CARNOW_EMAIL_API_KEY=your-email-api-key
# CARNOW_EMAIL_FROM=<EMAIL>

# SMS service configuration (if needed)
# CARNOW_SMS_PROVIDER=twilio
# CARNOW_SMS_ACCOUNT_SID=your-twilio-account-sid
# CARNOW_SMS_AUTH_TOKEN=your-twilio-auth-token

# File storage configuration (if needed)
# CARNOW_STORAGE_PROVIDER=s3
# CARNOW_STORAGE_BUCKET=carnow-production
# CARNOW_STORAGE_REGION=us-east-1
# CARNOW_STORAGE_ACCESS_KEY=your-access-key
# CARNOW_STORAGE_SECRET_KEY=your-secret-key

# =============================================================================
# KUBERNETES/DOCKER SPECIFIC
# =============================================================================
# Pod/Container specific settings
# CARNOW_POD_NAME=${HOSTNAME}
# CARNOW_POD_NAMESPACE=carnow-production
# CARNOW_CONTAINER_NAME=carnow-backend

# Resource limits awareness
# CARNOW_MEMORY_LIMIT=512Mi
# CARNOW_CPU_LIMIT=500m

# =============================================================================
# NOTES
# =============================================================================
# 1. Replace all placeholder values with actual production values
# 2. Keep this file secure and never commit to version control
# 3. Use Kubernetes secrets or similar for sensitive values in production
# 4. Validate all configuration before deployment
# 5. Test all endpoints after deployment
# 6. Monitor logs for any configuration errors
# 7. Ensure Supabase project is properly configured
# 8. Verify Google OAuth settings match your domain
# 9. Test authentication flows thoroughly
# 10. Follow Forever Plan principles - real data only, no mock data
