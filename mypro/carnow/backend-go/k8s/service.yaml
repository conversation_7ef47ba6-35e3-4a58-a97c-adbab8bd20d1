# CarNow Backend - Kubernetes Service
apiVersion: v1
kind: Service
metadata:
  name: carnow-backend-service
  namespace: carnow-production
  labels:
    app: carnow-backend
    component: backend
    environment: production
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  selector:
    app: carnow-backend

---
# Headless Service for StatefulSet (if needed)
apiVersion: v1
kind: Service
metadata:
  name: carnow-backend-headless
  namespace: carnow-production
  labels:
    app: carnow-backend
    component: backend
    environment: production
spec:
  clusterIP: None
  ports:
  - name: http
    port: 8080
    targetPort: http
    protocol: TCP
  selector:
    app: carnow-backend

---
# Load Balancer Service (for external access)
apiVersion: v1
kind: Service
metadata:
  name: carnow-backend-lb
  namespace: carnow-production
  labels:
    app: carnow-backend
    component: backend
    environment: production
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-path: "/health"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-interval: "30"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-timeout: "10"
    service.beta.kubernetes.io/aws-load-balancer-healthy-threshold: "2"
    service.beta.kubernetes.io/aws-load-balancer-unhealthy-threshold: "3"
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  - name: https
    port: 443
    targetPort: http
    protocol: TCP
  selector:
    app: carnow-backend
  sessionAffinity: None

---
# Ingress for HTTP/HTTPS routing
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: carnow-backend-ingress
  namespace: carnow-production
  labels:
    app: carnow-backend
    component: backend
    environment: production
  annotations:
    # Ingress class
    kubernetes.io/ingress.class: "nginx"
    
    # SSL/TLS configuration
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # Rate limiting
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    # CORS configuration
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://carnow.app,https://www.carnow.app"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET,POST,PUT,DELETE,OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Content-Type,Authorization,X-Requested-With"
    
    # Security headers
    nginx.ingress.kubernetes.io/server-snippet: |
      add_header X-Frame-Options "SAMEORIGIN" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Referrer-Policy "strict-origin-when-cross-origin" always;
      add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://lpxtghyvxuenyyisrrro.supabase.co;" always;
    
    # Health check configuration
    nginx.ingress.kubernetes.io/health-check-path: "/health"
    nginx.ingress.kubernetes.io/health-check-interval: "30s"
    nginx.ingress.kubernetes.io/health-check-timeout: "10s"
    
    # Load balancing
    nginx.ingress.kubernetes.io/load-balance: "round_robin"
    nginx.ingress.kubernetes.io/upstream-keepalive-connections: "10"
    
    # Request/Response configuration
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "10"
    
    # Monitoring
    nginx.ingress.kubernetes.io/enable-access-log: "true"
    nginx.ingress.kubernetes.io/enable-rewrite-log: "true"
spec:
  tls:
  - hosts:
    - api.carnow.app
    - backend.carnow.app
    secretName: carnow-backend-tls
  
  rules:
  - host: api.carnow.app
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: carnow-backend-service
            port:
              number: 80
  
  - host: backend.carnow.app
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: carnow-backend-service
            port:
              number: 80

---
# Network Policy for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: carnow-backend-netpol
  namespace: carnow-production
  labels:
    app: carnow-backend
    component: backend
    environment: production
spec:
  podSelector:
    matchLabels:
      app: carnow-backend
  
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # Allow ingress from nginx ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8080
  
  # Allow ingress from monitoring namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8080
  
  # Allow ingress from same namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: carnow-production
    ports:
    - protocol: TCP
      port: 8080
  
  egress:
  # Allow egress to Supabase
  - to: []
    ports:
    - protocol: TCP
      port: 443
  
  # Allow egress to DNS
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow egress to Redis (if in same cluster)
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379

---
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: carnow-backend-hpa
  namespace: carnow-production
  labels:
    app: carnow-backend
    component: backend
    environment: production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: carnow-backend
  
  minReplicas: 3
  maxReplicas: 10
  
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max

---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: carnow-backend-pdb
  namespace: carnow-production
  labels:
    app: carnow-backend
    component: backend
    environment: production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: carnow-backend
