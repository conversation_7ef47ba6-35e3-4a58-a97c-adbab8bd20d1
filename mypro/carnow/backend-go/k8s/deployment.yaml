# CarNow Backend - Kubernetes Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: carnow-backend
  namespace: carnow-production
  labels:
    app: carnow-backend
    version: v1.0.0
    component: backend
    environment: production
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: carnow-backend
  template:
    metadata:
      labels:
        app: carnow-backend
        version: v1.0.0
        component: backend
        environment: production
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      # Security context
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
      
      # Service account
      serviceAccountName: carnow-backend
      
      # Image pull secrets
      imagePullSecrets:
        - name: carnow-registry-secret
      
      containers:
      - name: carnow-backend
        image: carnow-backend:latest
        imagePullPolicy: Always
        
        # Ports
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        
        # Environment variables from ConfigMap
        envFrom:
        - configMapRef:
            name: carnow-backend-config
        
        # Environment variables from Secrets
        env:
        - name: CARNOW_SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: carnow-secrets
              key: supabase-anon-key
        - name: CARNOW_SUPABASE_SERVICE_ROLE_KEY
          valueFrom:
            secretKeyRef:
              name: carnow-secrets
              key: supabase-service-role-key
        - name: CARNOW_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: carnow-secrets
              key: jwt-secret
        - name: CARNOW_GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: carnow-secrets
              key: google-client-secret
        
        # Resource limits and requests
        resources:
          limits:
            cpu: 1000m
            memory: 512Mi
          requests:
            cpu: 500m
            memory: 256Mi
        
        # Security context
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          capabilities:
            drop:
            - ALL
        
        # Liveness probe
        livenessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        
        # Readiness probe
        readinessProbe:
          httpGet:
            path: /health/enhanced-auth
            port: http
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3
        
        # Startup probe
        startupProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 30
        
        # Volume mounts
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: cache
          mountPath: /app/cache
      
      # Volumes
      volumes:
      - name: tmp
        emptyDir: {}
      - name: cache
        emptyDir: {}
      
      # Node selector (optional)
      nodeSelector:
        kubernetes.io/os: linux
      
      # Tolerations (optional)
      tolerations:
      - key: "carnow.app/backend"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      
      # Affinity rules
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - carnow-backend
              topologyKey: kubernetes.io/hostname

---
# Service Account
apiVersion: v1
kind: ServiceAccount
metadata:
  name: carnow-backend
  namespace: carnow-production
  labels:
    app: carnow-backend
    component: backend
    environment: production

---
# ConfigMap for non-sensitive configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: carnow-backend-config
  namespace: carnow-production
  labels:
    app: carnow-backend
    component: backend
    environment: production
data:
  CARNOW_ENV: "production"
  CARNOW_SERVER_HOST: "0.0.0.0"
  CARNOW_SERVER_PORT: "8080"
  CARNOW_DEBUG: "false"
  CARNOW_SUPABASE_URL: "https://lpxtghyvxuenyyisrrro.supabase.co"
  CARNOW_SUPABASE_PROJECT_REF: "lpxtghyvxuenyyisrrro"
  CARNOW_JWT_EXPIRY: "24h"
  CARNOW_LOG_LEVEL: "info"
  CARNOW_LOG_FORMAT: "json"
  CARNOW_CORS_ALLOWED_ORIGINS: "https://carnow.app,https://www.carnow.app"
  CARNOW_CORS_ALLOWED_METHODS: "GET,POST,PUT,DELETE,OPTIONS"
  CARNOW_CORS_ALLOWED_HEADERS: "Content-Type,Authorization,X-Requested-With"
  CARNOW_RATE_LIMIT_REQUESTS_PER_MINUTE: "60"
  CARNOW_RATE_LIMIT_BURST: "10"
  CARNOW_SECURITY_HEADERS_ENABLED: "true"
  CARNOW_HEALTH_CHECK_INTERVAL: "30s"
  CARNOW_HEALTH_CHECK_TIMEOUT: "10s"
  CARNOW_METRICS_ENABLED: "true"
  CARNOW_METRICS_PATH: "/metrics"
  CARNOW_REQUEST_TIMEOUT: "30s"
  CARNOW_READ_TIMEOUT: "10s"
  CARNOW_WRITE_TIMEOUT: "10s"
  CARNOW_IDLE_TIMEOUT: "60s"
  CARNOW_MOCK_DATA_DETECTION: "true"
  CARNOW_REAL_DATA_VALIDATION: "true"
  CARNOW_PRODUCTION_EXCELLENCE: "true"
  CARNOW_DB_MAX_CONNECTIONS: "25"
  CARNOW_DB_MAX_IDLE_CONNECTIONS: "5"
  CARNOW_DB_CONNECTION_MAX_LIFETIME: "300s"

---
# Secret for sensitive configuration
apiVersion: v1
kind: Secret
metadata:
  name: carnow-secrets
  namespace: carnow-production
  labels:
    app: carnow-backend
    component: backend
    environment: production
type: Opaque
data:
  # Base64 encoded values - replace with actual values
  supabase-anon-key: eW91ci1zdXBhYmFzZS1hbm9uLWtleS1oZXJl  # your-supabase-anon-key-here
  supabase-service-role-key: eW91ci1zdXBhYmFzZS1zZXJ2aWNlLXJvbGUta2V5LWhlcmU=  # your-supabase-service-role-key-here
  jwt-secret: eW91ci1zdXBlci1zZWN1cmUtand0LXNlY3JldC1rZXktbWluaW11bS0zMi1jaGFyYWN0ZXJz  # your-super-secure-jwt-secret-key-minimum-32-characters
  google-client-secret: eW91ci1nb29nbGUtb2F1dGgtY2xpZW50LXNlY3JldA==  # your-google-oauth-client-secret
