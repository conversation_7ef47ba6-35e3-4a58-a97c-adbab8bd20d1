package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow-backend/internal/config"
	"carnow-backend/internal/shared/monitoring"
)

// HealthHandler handles health check endpoints
// Task 1.3: Enhanced health handler with configuration validation
type HealthHandler struct {
	logger        *zap.Logger
	authMonitor   *monitoring.AuthMonitor
	startTime     time.Time
	config        *config.Config
	healthChecker *config.HealthChecker
}

// NewHealthHandler creates a new health handler
// Task 1.3: Enhanced constructor with configuration validation
func NewHealthHandler(logger *zap.Logger, authMonitor *monitoring.AuthMonitor, cfg *config.Config) *HealthHandler {
	return &HealthHandler{
		logger:        logger,
		authMonitor:   authMonitor,
		startTime:     time.Now(),
		config:        cfg,
		healthChecker: config.NewHealthChecker(cfg),
	}
}

// HealthResponse represents the health check response
type HealthResponse struct {
	Status    string                 `json:"status"`
	Timestamp time.Time              `json:"timestamp"`
	Uptime    string                 `json:"uptime"`
	Version   string                 `json:"version"`
	Services  map[string]interface{} `json:"services"`
}

// ServiceHealth represents the health of a specific service
type ServiceHealth struct {
	Status      string                 `json:"status"`
	LastChecked time.Time              `json:"last_checked"`
	Details     map[string]interface{} `json:"details,omitempty"`
	Error       string                 `json:"error,omitempty"`
}

// GetHealth returns overall system health
func (h *HealthHandler) GetHealth(c *gin.Context) {
	uptime := time.Since(h.startTime)

	response := HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now(),
		Uptime:    uptime.String(),
		Version:   "1.0.0", // Should come from build info
		Services:  make(map[string]interface{}),
	}

	// Check authentication service health
	if h.authMonitor != nil {
		authHealth := h.authMonitor.GetHealthStatus()
		response.Services["authentication"] = authHealth

		// Update overall status based on auth health
		if status, ok := authHealth["status"].(string); ok && status != "healthy" {
			response.Status = status
		}
	}

	// Add database health check
	response.Services["database"] = h.checkDatabaseHealth()

	// Add Redis health check
	response.Services["redis"] = h.checkRedisHealth()

	// Determine overall status
	overallStatus := h.determineOverallStatus(response.Services)
	response.Status = overallStatus

	// Set appropriate HTTP status code
	statusCode := http.StatusOK
	if overallStatus == "degraded" {
		statusCode = http.StatusOK // Still return 200 for degraded
	} else if overallStatus == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	}

	h.logger.Info("Health check requested",
		zap.String("status", response.Status),
		zap.Duration("uptime", uptime),
		zap.String("client_ip", c.ClientIP()),
	)

	c.JSON(statusCode, response)
}

// GetAuthHealth returns authentication service specific health
func (h *HealthHandler) GetAuthHealth(c *gin.Context) {
	if h.authMonitor == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status": "unavailable",
			"error":  "Authentication monitoring not available",
		})
		return
	}

	authHealth := h.authMonitor.GetHealthStatus()
	authMetrics := h.authMonitor.GetMetrics()

	response := map[string]interface{}{
		"status":    authHealth["status"],
		"timestamp": time.Now(),
		"metrics": map[string]interface{}{
			"total_requests":        authMetrics.TotalRequests,
			"successful_requests":   authMetrics.SuccessfulRequests,
			"failed_requests":       authMetrics.FailedRequests,
			"success_rate":          float64(authMetrics.SuccessfulRequests) / max(float64(authMetrics.TotalRequests), 1),
			"average_response_time": authMetrics.AverageResponseTime.Milliseconds(),
			"slow_requests":         authMetrics.SlowRequests,
			"failed_login_attempts": authMetrics.FailedLoginAttempts,
			"suspicious_activities": authMetrics.SuspiciousActivities,
			"unique_users":          authMetrics.UniqueUsers,
			"last_request_time":     authMetrics.LastRequestTime,
			"last_failure_time":     authMetrics.LastFailureTime,
		},
		"endpoints":   authMetrics.RequestsByEndpoint,
		"providers":   authMetrics.UsersByProvider,
		"blocked_ips": len(authMetrics.BlockedIPs),
	}

	// Set appropriate status code
	statusCode := http.StatusOK
	if status, ok := authHealth["status"].(string); ok {
		if status == "unhealthy" {
			statusCode = http.StatusServiceUnavailable
		}
	}

	h.logger.Info("Auth health check requested",
		zap.String("status", authHealth["status"].(string)),
		zap.String("client_ip", c.ClientIP()),
	)

	c.JSON(statusCode, response)
}

// GetReadiness returns readiness probe for Kubernetes
func (h *HealthHandler) GetReadiness(c *gin.Context) {
	// Check if all critical services are ready
	ready := true
	services := make(map[string]bool)

	// Check database readiness
	dbHealth := h.checkDatabaseHealth()
	if status, ok := dbHealth["status"].(string); ok {
		services["database"] = status == "healthy"
		if status != "healthy" {
			ready = false
		}
	}

	// Check authentication service readiness
	if h.authMonitor != nil {
		authHealth := h.authMonitor.GetHealthStatus()
		if status, ok := authHealth["status"].(string); ok {
			services["authentication"] = status != "unhealthy"
			if status == "unhealthy" {
				ready = false
			}
		}
	}

	response := map[string]interface{}{
		"ready":     ready,
		"timestamp": time.Now(),
		"services":  services,
	}

	statusCode := http.StatusOK
	if !ready {
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, response)
}

// GetLiveness returns liveness probe for Kubernetes
func (h *HealthHandler) GetLiveness(c *gin.Context) {
	// Simple liveness check - if we can respond, we're alive
	response := map[string]interface{}{
		"alive":     true,
		"timestamp": time.Now(),
		"uptime":    time.Since(h.startTime).String(),
	}

	c.JSON(http.StatusOK, response)
}

// GetMetrics returns Prometheus-style metrics
func (h *HealthHandler) GetMetrics(c *gin.Context) {
	if h.authMonitor == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Metrics not available",
		})
		return
	}

	metrics := h.authMonitor.GetMetrics()

	// Format metrics in Prometheus style
	prometheusMetrics := []string{
		fmt.Sprintf("# HELP auth_requests_total Total number of authentication requests"),
		fmt.Sprintf("# TYPE auth_requests_total counter"),
		fmt.Sprintf("auth_requests_total %d", metrics.TotalRequests),
		fmt.Sprintf(""),
		fmt.Sprintf("# HELP auth_requests_successful_total Total number of successful authentication requests"),
		fmt.Sprintf("# TYPE auth_requests_successful_total counter"),
		fmt.Sprintf("auth_requests_successful_total %d", metrics.SuccessfulRequests),
		fmt.Sprintf(""),
		fmt.Sprintf("# HELP auth_requests_failed_total Total number of failed authentication requests"),
		fmt.Sprintf("# TYPE auth_requests_failed_total counter"),
		fmt.Sprintf("auth_requests_failed_total %d", metrics.FailedRequests),
		fmt.Sprintf(""),
		fmt.Sprintf("# HELP auth_response_time_avg Average response time in milliseconds"),
		fmt.Sprintf("# TYPE auth_response_time_avg gauge"),
		fmt.Sprintf("auth_response_time_avg %d", metrics.AverageResponseTime.Milliseconds()),
		fmt.Sprintf(""),
		fmt.Sprintf("# HELP auth_slow_requests_total Total number of slow requests"),
		fmt.Sprintf("# TYPE auth_slow_requests_total counter"),
		fmt.Sprintf("auth_slow_requests_total %d", metrics.SlowRequests),
		fmt.Sprintf(""),
		fmt.Sprintf("# HELP auth_failed_login_attempts_total Total number of failed login attempts"),
		fmt.Sprintf("# TYPE auth_failed_login_attempts_total counter"),
		fmt.Sprintf("auth_failed_login_attempts_total %d", metrics.FailedLoginAttempts),
		fmt.Sprintf(""),
		fmt.Sprintf("# HELP auth_suspicious_activities_total Total number of suspicious activities"),
		fmt.Sprintf("# TYPE auth_suspicious_activities_total counter"),
		fmt.Sprintf("auth_suspicious_activities_total %d", metrics.SuspiciousActivities),
		fmt.Sprintf(""),
		fmt.Sprintf("# HELP auth_unique_users_total Total number of unique users"),
		fmt.Sprintf("# TYPE auth_unique_users_total gauge"),
		fmt.Sprintf("auth_unique_users_total %d", metrics.UniqueUsers),
	}

	// Add per-endpoint metrics
	for endpoint, count := range metrics.RequestsByEndpoint {
		prometheusMetrics = append(prometheusMetrics,
			fmt.Sprintf("auth_requests_by_endpoint{endpoint=\"%s\"} %d", endpoint, count),
		)
	}

	// Add per-provider metrics
	for provider, count := range metrics.UsersByProvider {
		prometheusMetrics = append(prometheusMetrics,
			fmt.Sprintf("auth_users_by_provider{provider=\"%s\"} %d", provider, count),
		)
	}

	// Return as plain text for Prometheus
	c.Header("Content-Type", "text/plain")
	c.String(http.StatusOK, strings.Join(prometheusMetrics, "\n"))
}

// Helper methods

func (h *HealthHandler) checkDatabaseHealth() map[string]interface{} {
	// TODO: Implement actual database health check
	// For now, return a mock healthy status
	return map[string]interface{}{
		"status":       "healthy",
		"last_checked": time.Now(),
		"details": map[string]interface{}{
			"connection_pool": "healthy",
			"query_time":      "< 100ms",
		},
	}
}

func (h *HealthHandler) checkRedisHealth() map[string]interface{} {
	// TODO: Implement actual Redis health check
	// For now, return a mock healthy status
	return map[string]interface{}{
		"status":       "healthy",
		"last_checked": time.Now(),
		"details": map[string]interface{}{
			"connection":   "healthy",
			"memory_usage": "< 50%",
		},
	}
}

func (h *HealthHandler) determineOverallStatus(services map[string]interface{}) string {
	hasUnhealthy := false
	hasDegraded := false

	for _, service := range services {
		if serviceMap, ok := service.(map[string]interface{}); ok {
			if status, ok := serviceMap["status"].(string); ok {
				switch status {
				case "unhealthy":
					hasUnhealthy = true
				case "degraded":
					hasDegraded = true
				}
			}
		}
	}

	if hasUnhealthy {
		return "unhealthy"
	}
	if hasDegraded {
		return "degraded"
	}
	return "healthy"
}

func max(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

// GetHealthSupabase handles GET /health/supabase endpoint
// Task 1.3: Specific Supabase health check endpoint
func (h *HealthHandler) GetHealthSupabase(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 15*time.Second)
	defer cancel()

	result := h.healthChecker.CheckSupabaseHealth(ctx)

	statusCode := http.StatusOK
	if result.Status == "degraded" {
		statusCode = http.StatusPartialContent
	} else if result.Status == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	}

	h.logger.Info("Supabase health check completed",
		zap.String("status", result.Status),
		zap.Duration("response_time", result.ResponseTime),
	)

	c.JSON(statusCode, result)
}

// GetHealthDatabase handles GET /health/database endpoint
// Task 1.3: Specific database health check endpoint
func (h *HealthHandler) GetHealthDatabase(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 15*time.Second)
	defer cancel()

	result := h.healthChecker.CheckDatabaseHealth(ctx)

	statusCode := http.StatusOK
	if result.Status == "degraded" {
		statusCode = http.StatusPartialContent
	} else if result.Status == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	}

	h.logger.Info("Database health check completed",
		zap.String("status", result.Status),
		zap.Duration("response_time", result.ResponseTime),
	)

	c.JSON(statusCode, result)
}

// GetHealthConfig handles GET /health/config endpoint
// Task 1.3: Configuration validation endpoint
func (h *HealthHandler) GetHealthConfig(c *gin.Context) {
	start := time.Now()

	// Validate environment variables
	envValidationError := h.healthChecker.ValidateEnvironmentVariables()

	// Create configuration health result
	result := &config.HealthCheckResult{
		Service:      "configuration",
		Status:       "healthy",
		Message:      "Configuration is valid",
		ResponseTime: time.Since(start),
		Timestamp:    start,
		Details:      make(map[string]interface{}),
	}

	if envValidationError != nil {
		result.Status = "unhealthy"
		result.Message = envValidationError.Error()
		result.Details["validation_error"] = envValidationError.Error()
	}

	// Add configuration details (without sensitive information)
	result.Details["environment"] = h.config.App.Environment
	result.Details["app_name"] = h.config.App.Name
	result.Details["app_version"] = h.config.App.Version
	result.Details["supabase_url_configured"] = h.config.Supabase.URL != ""
	result.Details["jwt_configured"] = h.config.JWT.Secret != ""
	result.Details["database_configured"] = h.config.Database.Host != ""

	statusCode := http.StatusOK
	if result.Status == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	}

	h.logger.Info("Configuration health check completed",
		zap.String("status", result.Status),
		zap.Duration("response_time", result.ResponseTime),
	)

	c.JSON(statusCode, result)
}

// GetHealthReadiness handles GET /health/readiness endpoint
// Task 1.3: Kubernetes readiness probe endpoint
func (h *HealthHandler) GetHealthReadiness(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
	defer cancel()

	// Check if all critical services are ready
	supabaseResult := h.healthChecker.CheckSupabaseHealth(ctx)

	ready := supabaseResult.Status == "healthy" || supabaseResult.Status == "degraded"

	response := gin.H{
		"ready":     ready,
		"timestamp": time.Now(),
		"checks": gin.H{
			"supabase": supabaseResult.Status,
		},
	}

	statusCode := http.StatusOK
	if !ready {
		statusCode = http.StatusServiceUnavailable
	}

	h.logger.Info("Readiness check completed",
		zap.Bool("ready", ready),
		zap.String("supabase_status", supabaseResult.Status),
	)

	c.JSON(statusCode, response)
}

// GetHealthLiveness handles GET /health/liveness endpoint
// Task 1.3: Kubernetes liveness probe endpoint
func (h *HealthHandler) GetHealthLiveness(c *gin.Context) {
	// Simple liveness check - just return that the service is alive
	response := gin.H{
		"alive":     true,
		"timestamp": time.Now(),
		"service":   "carnow-backend",
		"uptime":    time.Since(h.startTime).String(),
	}

	h.logger.Debug("Liveness check completed")
	c.JSON(http.StatusOK, response)
}
