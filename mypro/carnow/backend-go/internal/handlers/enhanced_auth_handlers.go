package handlers

import (
	"context"
	"log"
	"net/http"
	"regexp"
	"strings"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/services"
	"carnow-backend/internal/shared/errors"
	sharedservices "carnow-backend/internal/shared/services"

	"github.com/gin-gonic/gin"
)

// =============================================================================
// Task 3.1: Enhanced Authentication Handlers with Supabase Integration
// =============================================================================

// SupabaseAuthServiceInterface defines the interface for Supabase authentication service
type SupabaseAuthServiceInterface interface {
	SignInWithEmail(ctx context.Context, email, password string) (*services.AuthResponse, error)
	SignOut(ctx context.Context, accessToken string) error
	RefreshToken(ctx context.Context, refreshToken string) (*services.AuthResponse, error)
	GetUser(ctx context.Context, accessToken string) (*services.User, error)
}

// EnhancedAuthHandlers implements enhanced authentication using Supabase Auth Service
// Following Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data + Auth)
type EnhancedAuthHandlers struct {
	supabaseAuthService SupabaseAuthServiceInterface
	jwtService          *sharedservices.JWTService
	googleService       *sharedservices.GoogleOAuthService
	config              *config.Config
}

// NewEnhancedAuthHandlers creates a new enhanced auth handler with Supabase integration
// Task 3.1: Create enhanced auth handlers with Supabase auth service
func NewEnhancedAuthHandlers(
	supabaseAuthService SupabaseAuthServiceInterface,
	jwtService *sharedservices.JWTService,
	googleService *sharedservices.GoogleOAuthService,
	config *config.Config,
) *EnhancedAuthHandlers {
	return &EnhancedAuthHandlers{
		supabaseAuthService: supabaseAuthService,
		jwtService:          jwtService,
		googleService:       googleService,
		config:              config,
	}
}

// =============================================================================
// Request/Response Models for Enhanced Authentication
// =============================================================================

// EnhancedLoginRequest represents the enhanced login request payload
type EnhancedLoginRequest struct {
	Email    string `json:"email" binding:"required,email" validate:"required,email,max=255"`
	Password string `json:"password" binding:"required,min=6" validate:"required,min=6,max=128"`
}

// EnhancedRegisterRequest represents the enhanced registration request payload
type EnhancedRegisterRequest struct {
	Email           string `json:"email" binding:"required,email" validate:"required,email,max=255"`
	Password        string `json:"password" binding:"required,min=6" validate:"required,min=6,max=128"`
	FirstName       string `json:"first_name" binding:"required,min=2" validate:"required,min=2,max=50"`
	LastName        string `json:"last_name" binding:"required,min=2" validate:"required,min=2,max=50"`
	ConfirmPassword string `json:"confirm_password" binding:"required" validate:"required,eqfield=Password"`
}

// EnhancedRefreshTokenRequest represents the enhanced token refresh request payload
type EnhancedRefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required" validate:"required,min=10"`
}

// EnhancedAuthResponse represents the enhanced authentication response
type EnhancedAuthResponse struct {
	Success      bool                   `json:"success"`
	AccessToken  string                 `json:"access_token,omitempty"`
	RefreshToken string                 `json:"refresh_token,omitempty"`
	ExpiresAt    int64                  `json:"expires_at,omitempty"`
	ExpiresIn    int                    `json:"expires_in,omitempty"`
	TokenType    string                 `json:"token_type,omitempty"`
	User         map[string]interface{} `json:"user,omitempty"`
	Message      string                 `json:"message"`
	Error        string                 `json:"error,omitempty"`
	Code         string                 `json:"code,omitempty"`
}

// =============================================================================
// Task 3.1: Enhanced Login Endpoint with Supabase Integration
// =============================================================================

// EnhancedLogin handles POST /auth/login endpoint with Supabase authentication
// Task 3.1: Update login endpoint to use Supabase auth service
func (h *EnhancedAuthHandlers) EnhancedLogin(c *gin.Context) {
	var req EnhancedLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("❌ Enhanced Login: Invalid request payload: %v", err)
		c.JSON(http.StatusBadRequest, EnhancedAuthResponse{
			Success: false,
			Error:   "Invalid request payload",
			Code:    "INVALID_REQUEST",
			Message: "Please provide valid email and password",
		})
		return
	}

	// Validate input for Forever Plan compliance (no mock data)
	if err := h.validateLoginInput(req.Email, req.Password); err != nil {
		log.Printf("❌ Enhanced Login: Input validation failed: %v", err)
		c.JSON(http.StatusBadRequest, EnhancedAuthResponse{
			Success: false,
			Error:   "Invalid input",
			Code:    "VALIDATION_FAILED",
			Message: err.Error(),
		})
		return
	}

	log.Printf("🔐 Enhanced Login: Attempting login for %s", req.Email)

	// Authenticate with Supabase Auth Service
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	authResponse, err := h.supabaseAuthService.SignInWithEmail(ctx, req.Email, req.Password)
	if err != nil {
		log.Printf("❌ Enhanced Login: Supabase authentication failed for %s: %v", req.Email, err)

		// Handle specific error types
		if authErr, ok := err.(*errors.AuthError); ok {
			c.JSON(authErr.HTTPStatus, EnhancedAuthResponse{
				Success: false,
				Error:   authErr.Message,
				Code:    string(authErr.Code),
				Message: "Authentication failed",
			})
			return
		}

		c.JSON(http.StatusUnauthorized, EnhancedAuthResponse{
			Success: false,
			Error:   "Authentication failed",
			Code:    "AUTH_FAILED",
			Message: "Invalid email or password",
		})
		return
	}

	// Auth response is already validated by the service
	// No additional validation needed here

	log.Printf("✅ Enhanced Login: Successful authentication for %s (ID: %s)",
		authResponse.User.Email, authResponse.User.ID)

	// Prepare user data for response
	userData := map[string]interface{}{
		"id":         authResponse.User.ID,
		"email":      authResponse.User.Email,
		"name":       authResponse.User.Email, // Use email as display name
		"provider":   "email",
		"created_at": authResponse.User.CreatedAt,
		"updated_at": authResponse.User.UpdatedAt,
	}

	// Add metadata if available
	if authResponse.User.UserMetadata != nil {
		userData["metadata"] = authResponse.User.UserMetadata
	}

	c.JSON(http.StatusOK, EnhancedAuthResponse{
		Success:      true,
		AccessToken:  authResponse.AccessToken,
		RefreshToken: authResponse.RefreshToken,
		ExpiresAt:    authResponse.ExpiresAt.Unix(),
		ExpiresIn:    authResponse.ExpiresIn,
		TokenType:    authResponse.TokenType,
		User:         userData,
		Message:      "Login successful",
	})
}

// =============================================================================
// Task 3.1: Enhanced Logout Endpoint with Supabase Integration
// =============================================================================

// EnhancedLogout handles POST /auth/logout endpoint with Supabase sign out
// Task 3.1: Update logout endpoint to use Supabase auth service
func (h *EnhancedAuthHandlers) EnhancedLogout(c *gin.Context) {
	// Get access token from Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		log.Printf("❌ Enhanced Logout: No authorization header provided")
		c.JSON(http.StatusBadRequest, EnhancedAuthResponse{
			Success: false,
			Error:   "Authorization header required",
			Code:    "AUTH_HEADER_MISSING",
			Message: "Please provide a valid authorization token",
		})
		return
	}

	// Extract Bearer token
	tokenParts := strings.Split(authHeader, " ")
	if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
		log.Printf("❌ Enhanced Logout: Invalid authorization header format")
		c.JSON(http.StatusBadRequest, EnhancedAuthResponse{
			Success: false,
			Error:   "Invalid authorization header format",
			Code:    "AUTH_HEADER_INVALID",
			Message: "Authorization header must be in format: Bearer <token>",
		})
		return
	}

	accessToken := tokenParts[1]
	log.Printf("🔐 Enhanced Logout: Attempting logout with token")

	// Sign out with Supabase Auth Service
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err := h.supabaseAuthService.SignOut(ctx, accessToken)
	if err != nil {
		log.Printf("❌ Enhanced Logout: Supabase sign out failed: %v", err)

		// Handle specific error types
		if authErr, ok := err.(*errors.AuthError); ok {
			c.JSON(authErr.HTTPStatus, EnhancedAuthResponse{
				Success: false,
				Error:   authErr.Message,
				Code:    string(authErr.Code),
				Message: "Logout failed",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, EnhancedAuthResponse{
			Success: false,
			Error:   "Logout failed",
			Code:    "LOGOUT_FAILED",
			Message: "Internal server error",
		})
		return
	}

	log.Printf("✅ Enhanced Logout: Successful logout")

	c.JSON(http.StatusOK, EnhancedAuthResponse{
		Success: true,
		Message: "Logout successful",
	})
}

// =============================================================================
// Task 3.1: Enhanced Token Refresh Endpoint with Supabase Integration
// =============================================================================

// EnhancedRefreshToken handles POST /auth/refresh endpoint with Supabase token refresh
// Task 3.1: Add token refresh endpoint using Supabase auth service
func (h *EnhancedAuthHandlers) EnhancedRefreshToken(c *gin.Context) {
	var req EnhancedRefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("❌ Enhanced Refresh Token: Invalid request payload: %v", err)
		c.JSON(http.StatusBadRequest, EnhancedAuthResponse{
			Success: false,
			Error:   "Invalid request payload",
			Code:    "INVALID_REQUEST",
			Message: "Please provide a valid refresh token",
		})
		return
	}

	log.Printf("🔐 Enhanced Refresh Token: Attempting token refresh")

	// Refresh token with Supabase Auth Service
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	authResponse, err := h.supabaseAuthService.RefreshToken(ctx, req.RefreshToken)
	if err != nil {
		log.Printf("❌ Enhanced Refresh Token: Supabase token refresh failed: %v", err)

		// Handle specific error types
		if authErr, ok := err.(*errors.AuthError); ok {
			c.JSON(authErr.HTTPStatus, EnhancedAuthResponse{
				Success: false,
				Error:   authErr.Message,
				Code:    string(authErr.Code),
				Message: "Token refresh failed",
			})
			return
		}

		c.JSON(http.StatusUnauthorized, EnhancedAuthResponse{
			Success: false,
			Error:   "Token refresh failed",
			Code:    "TOKEN_REFRESH_FAILED",
			Message: "Invalid or expired refresh token",
		})
		return
	}

	// Auth response is already validated by the service
	// No additional validation needed here

	log.Printf("✅ Enhanced Refresh Token: Successful token refresh for user %s",
		authResponse.User.Email)

	// Prepare user data for response
	userData := map[string]interface{}{
		"id":         authResponse.User.ID,
		"email":      authResponse.User.Email,
		"name":       authResponse.User.Email, // Use email as display name
		"provider":   "email",
		"created_at": authResponse.User.CreatedAt,
		"updated_at": authResponse.User.UpdatedAt,
	}

	c.JSON(http.StatusOK, EnhancedAuthResponse{
		Success:      true,
		AccessToken:  authResponse.AccessToken,
		RefreshToken: authResponse.RefreshToken,
		ExpiresAt:    authResponse.ExpiresAt.Unix(),
		ExpiresIn:    authResponse.ExpiresIn,
		TokenType:    authResponse.TokenType,
		User:         userData,
		Message:      "Token refresh successful",
	})
}

// =============================================================================
// Task 3.1: Enhanced Get User Endpoint with Supabase Integration
// =============================================================================

// EnhancedGetUser handles GET /auth/user endpoint with Supabase user retrieval
// Task 3.1: Add get user endpoint using Supabase auth service
func (h *EnhancedAuthHandlers) EnhancedGetUser(c *gin.Context) {
	// Get access token from Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		log.Printf("❌ Enhanced Get User: No authorization header provided")
		c.JSON(http.StatusBadRequest, EnhancedAuthResponse{
			Success: false,
			Error:   "Authorization header required",
			Code:    "AUTH_HEADER_MISSING",
			Message: "Please provide a valid authorization token",
		})
		return
	}

	// Extract Bearer token
	tokenParts := strings.Split(authHeader, " ")
	if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
		log.Printf("❌ Enhanced Get User: Invalid authorization header format")
		c.JSON(http.StatusBadRequest, EnhancedAuthResponse{
			Success: false,
			Error:   "Invalid authorization header format",
			Code:    "AUTH_HEADER_INVALID",
			Message: "Authorization header must be in format: Bearer <token>",
		})
		return
	}

	accessToken := tokenParts[1]
	log.Printf("🔐 Enhanced Get User: Attempting to get user with token")

	// Get user with Supabase Auth Service
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	user, err := h.supabaseAuthService.GetUser(ctx, accessToken)
	if err != nil {
		log.Printf("❌ Enhanced Get User: Supabase get user failed: %v", err)

		// Handle specific error types
		if authErr, ok := err.(*errors.AuthError); ok {
			c.JSON(authErr.HTTPStatus, EnhancedAuthResponse{
				Success: false,
				Error:   authErr.Message,
				Code:    string(authErr.Code),
				Message: "Failed to get user",
			})
			return
		}

		c.JSON(http.StatusUnauthorized, EnhancedAuthResponse{
			Success: false,
			Error:   "Failed to get user",
			Code:    "GET_USER_FAILED",
			Message: "Invalid or expired token",
		})
		return
	}

	// User is already validated by the service
	// No additional validation needed here

	log.Printf("✅ Enhanced Get User: Successfully retrieved user %s (ID: %s)",
		user.Email, user.ID)

	// Prepare user data for response
	userData := map[string]interface{}{
		"id":         user.ID,
		"email":      user.Email,
		"name":       user.Email, // Use email as display name
		"provider":   "email",
		"created_at": user.CreatedAt,
		"updated_at": user.UpdatedAt,
	}

	// Add metadata if available
	if user.UserMetadata != nil {
		userData["metadata"] = user.UserMetadata
	}

	c.JSON(http.StatusOK, EnhancedAuthResponse{
		Success: true,
		User:    userData,
		Message: "User retrieved successfully",
	})
}

// =============================================================================
// Helper Functions for Enhanced Authentication
// =============================================================================

// validateLoginInput validates login input for Forever Plan compliance
// Task 3.1: Add input validation with Forever Plan compliance
func (h *EnhancedAuthHandlers) validateLoginInput(email, password string) error {
	// Check for mock data patterns (Forever Plan compliance)
	mockPatterns := []string{"mock_", "test_", "fake_", "sample_", "demo_", "example"}

	emailLower := strings.ToLower(email)
	passwordLower := strings.ToLower(password)

	for _, pattern := range mockPatterns {
		if strings.Contains(emailLower, pattern) {
			return errors.ErrMockDataDetected("email")
		}
		if strings.Contains(passwordLower, pattern) {
			return errors.ErrMockDataDetected("password")
		}
	}

	// Basic email validation
	if !isValidEmail(email) {
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "invalid email format")
	}

	// Password strength validation
	if len(password) < 6 {
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "password must be at least 6 characters")
	}

	return nil
}

// isValidEmail validates email format
func isValidEmail(email string) bool {
	// Basic email regex pattern
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	matched, _ := regexp.MatchString(emailRegex, email)
	return matched
}
