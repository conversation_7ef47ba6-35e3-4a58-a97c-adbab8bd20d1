package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/services"
	"carnow-backend/internal/shared/errors"
	sharedservices "carnow-backend/internal/shared/services"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// =============================================================================
// Task 3.1: Enhanced Authentication Handlers Tests
// =============================================================================

// MockSupabaseAuthService is a mock implementation of SupabaseAuthService
type MockSupabaseAuthService struct {
	mock.Mock
}

func (m *MockSupabaseAuthService) SignInWithEmail(ctx context.Context, email, password string) (*services.AuthResponse, error) {
	args := m.Called(ctx, email, password)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*services.AuthResponse), args.Error(1)
}

func (m *MockSupabaseAuthService) SignOut(ctx context.Context, accessToken string) error {
	args := m.Called(ctx, accessToken)
	return args.Error(0)
}

func (m *MockSupabaseAuthService) RefreshToken(ctx context.Context, refreshToken string) (*services.AuthResponse, error) {
	args := m.Called(ctx, refreshToken)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*services.AuthResponse), args.Error(1)
}

func (m *MockSupabaseAuthService) GetUser(ctx context.Context, accessToken string) (*services.User, error) {
	args := m.Called(ctx, accessToken)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*services.User), args.Error(1)
}

// createTestEnhancedAuthHandlers creates enhanced auth handlers for testing
func createTestEnhancedAuthHandlers() (*EnhancedAuthHandlers, *MockSupabaseAuthService) {
	mockSupabaseService := &MockSupabaseAuthService{}

	// Create test configuration
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret: "test-jwt-secret-key-for-testing-purposes-only",
		},
	}

	// Create mock services
	jwtService, _ := sharedservices.NewJWTService(cfg)
	googleService, _ := sharedservices.NewGoogleOAuthService("test-client-id", "test-client-secret")

	handlers := NewEnhancedAuthHandlers(
		mockSupabaseService,
		jwtService,
		googleService,
		cfg,
	)

	return handlers, mockSupabaseService
}

// =============================================================================
// Test Enhanced Login Endpoint
// =============================================================================

func TestEnhancedAuthHandlers_EnhancedLogin_Success(t *testing.T) {
	handlers, mockService := createTestEnhancedAuthHandlers()

	// Setup mock response
	mockAuthResponse := &services.AuthResponse{
		AccessToken:  "test-access-token",
		RefreshToken: "test-refresh-token",
		ExpiresIn:    3600,
		TokenType:    "Bearer",
		User: &services.User{
			ID:        "test-user-id",
			Email:     "<EMAIL>",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	mockService.On("SignInWithEmail", mock.Anything, "<EMAIL>", "password123").
		Return(mockAuthResponse, nil)

	// Create test request
	requestBody := EnhancedLoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}
	jsonBody, _ := json.Marshal(requestBody)

	// Setup Gin test context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/auth/login", bytes.NewBuffer(jsonBody))
	c.Request.Header.Set("Content-Type", "application/json")

	// Execute the handler
	handlers.EnhancedLogin(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response EnhancedAuthResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	assert.Equal(t, "test-access-token", response.AccessToken)
	assert.Equal(t, "test-refresh-token", response.RefreshToken)
	assert.Equal(t, "Login successful", response.Message)
	assert.NotNil(t, response.User)

	mockService.AssertExpectations(t)
}

func TestEnhancedAuthHandlers_EnhancedLogin_InvalidRequest(t *testing.T) {
	handlers, _ := createTestEnhancedAuthHandlers()

	// Create invalid request (missing password)
	requestBody := map[string]interface{}{
		"email": "<EMAIL>",
		// password missing
	}
	jsonBody, _ := json.Marshal(requestBody)

	// Setup Gin test context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/auth/login", bytes.NewBuffer(jsonBody))
	c.Request.Header.Set("Content-Type", "application/json")

	// Execute the handler
	handlers.EnhancedLogin(c)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response EnhancedAuthResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response.Success)
	assert.Equal(t, "INVALID_REQUEST", response.Code)
}

func TestEnhancedAuthHandlers_EnhancedLogin_MockDataDetection(t *testing.T) {
	handlers, _ := createTestEnhancedAuthHandlers()

	// Create request with mock data
	requestBody := EnhancedLoginRequest{
		Email:    "<EMAIL>", // Contains mock pattern
		Password: "password123",
	}
	jsonBody, _ := json.Marshal(requestBody)

	// Setup Gin test context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/auth/login", bytes.NewBuffer(jsonBody))
	c.Request.Header.Set("Content-Type", "application/json")

	// Execute the handler
	handlers.EnhancedLogin(c)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response EnhancedAuthResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response.Success)
	assert.Equal(t, "VALIDATION_FAILED", response.Code)
}

func TestEnhancedAuthHandlers_EnhancedLogin_AuthenticationFailed(t *testing.T) {
	handlers, mockService := createTestEnhancedAuthHandlers()

	// Setup mock to return authentication error
	authError := errors.NewAuthError(errors.ErrCodeAuthFailed, "Invalid credentials")
	mockService.On("SignInWithEmail", mock.Anything, "<EMAIL>", "wrongpassword").
		Return(nil, authError)

	// Create test request
	requestBody := EnhancedLoginRequest{
		Email:    "<EMAIL>",
		Password: "wrongpassword",
	}
	jsonBody, _ := json.Marshal(requestBody)

	// Setup Gin test context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/auth/login", bytes.NewBuffer(jsonBody))
	c.Request.Header.Set("Content-Type", "application/json")

	// Execute the handler
	handlers.EnhancedLogin(c)

	// Assertions
	assert.Equal(t, http.StatusUnauthorized, w.Code)

	var response EnhancedAuthResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response.Success)
	assert.Equal(t, "AUTH_FAILED", response.Code)

	mockService.AssertExpectations(t)
}

// =============================================================================
// Test Enhanced Logout Endpoint
// =============================================================================

func TestEnhancedAuthHandlers_EnhancedLogout_Success(t *testing.T) {
	handlers, mockService := createTestEnhancedAuthHandlers()

	// Setup mock to return success
	mockService.On("SignOut", mock.Anything, "test-access-token").
		Return(nil)

	// Setup Gin test context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/auth/logout", nil)
	c.Request.Header.Set("Authorization", "Bearer test-access-token")

	// Execute the handler
	handlers.EnhancedLogout(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response EnhancedAuthResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	assert.Equal(t, "Logout successful", response.Message)

	mockService.AssertExpectations(t)
}

func TestEnhancedAuthHandlers_EnhancedLogout_MissingAuthHeader(t *testing.T) {
	handlers, _ := createTestEnhancedAuthHandlers()

	// Setup Gin test context without Authorization header
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/auth/logout", nil)

	// Execute the handler
	handlers.EnhancedLogout(c)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response EnhancedAuthResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response.Success)
	assert.Equal(t, "AUTH_HEADER_MISSING", response.Code)
}

// =============================================================================
// Test Enhanced Refresh Token Endpoint
// =============================================================================

func TestEnhancedAuthHandlers_EnhancedRefreshToken_Success(t *testing.T) {
	handlers, mockService := createTestEnhancedAuthHandlers()

	// Setup mock response
	mockAuthResponse := &services.AuthResponse{
		AccessToken:  "new-access-token",
		RefreshToken: "new-refresh-token",
		ExpiresIn:    3600,
		TokenType:    "Bearer",
		User: &services.User{
			ID:        "test-user-id",
			Email:     "<EMAIL>",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	mockService.On("RefreshToken", mock.Anything, "test-refresh-token").
		Return(mockAuthResponse, nil)

	// Create test request
	requestBody := EnhancedRefreshTokenRequest{
		RefreshToken: "test-refresh-token",
	}
	jsonBody, _ := json.Marshal(requestBody)

	// Setup Gin test context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/auth/refresh", bytes.NewBuffer(jsonBody))
	c.Request.Header.Set("Content-Type", "application/json")

	// Execute the handler
	handlers.EnhancedRefreshToken(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response EnhancedAuthResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	assert.Equal(t, "new-access-token", response.AccessToken)
	assert.Equal(t, "Token refresh successful", response.Message)

	mockService.AssertExpectations(t)
}

// =============================================================================
// Test Enhanced Get User Endpoint
// =============================================================================

func TestEnhancedAuthHandlers_EnhancedGetUser_Success(t *testing.T) {
	handlers, mockService := createTestEnhancedAuthHandlers()

	// Setup mock response
	mockUser := &services.User{
		ID:        "test-user-id",
		Email:     "<EMAIL>",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	mockService.On("GetUser", mock.Anything, "test-access-token").
		Return(mockUser, nil)

	// Setup Gin test context
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/auth/user", nil)
	c.Request.Header.Set("Authorization", "Bearer test-access-token")

	// Execute the handler
	handlers.EnhancedGetUser(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response EnhancedAuthResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	assert.Equal(t, "User retrieved successfully", response.Message)
	assert.NotNil(t, response.User)

	mockService.AssertExpectations(t)
}
