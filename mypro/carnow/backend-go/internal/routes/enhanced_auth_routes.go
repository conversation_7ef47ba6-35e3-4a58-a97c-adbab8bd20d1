package routes

import (
	"carnow-backend/internal/config"
	"carnow-backend/internal/handlers"
	"carnow-backend/internal/services"
	sharedservices "carnow-backend/internal/shared/services"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// =============================================================================
// Task 3.1: Enhanced Authentication Routes with Supabase Integration
// =============================================================================

// SetupEnhancedAuthRoutes configures enhanced authentication routes with Supabase integration
// Task 3.1: Create enhanced auth routes using new Supabase auth service
func SetupEnhancedAuthRoutes(
	router *gin.Engine,
	config *config.Config,
	logger *zap.Logger,
) error {
	// Create Supabase Auth Service
	supabaseAuthService, err := services.NewSupabaseAuthService(config, logger)
	if err != nil {
		logger.Error("Failed to create Supabase auth service", zap.Error(err))
		return err
	}

	// Create shared services
	jwtService, err := sharedservices.NewJWTService(config)
	if err != nil {
		logger.Error("Failed to create JWT service", zap.Error(err))
		return err
	}

	googleService, err := sharedservices.NewGoogleOAuthService(
		config.Google.ClientID,
		config.Google.ClientSecret,
	)
	if err != nil {
		logger.Error("Failed to create Google OAuth service", zap.Error(err))
		return err
	}

	// Create Enhanced Auth Handlers
	enhancedAuthHandlers := handlers.NewEnhancedAuthHandlers(
		supabaseAuthService,
		jwtService,
		googleService,
		config,
	)

	// Setup enhanced authentication routes
	setupEnhancedAuthEndpoints(router, enhancedAuthHandlers, logger)

	logger.Info("✅ Enhanced authentication routes configured successfully")
	return nil
}

// setupEnhancedAuthEndpoints configures the enhanced authentication endpoints
// Task 3.1: Setup enhanced auth endpoints with proper routing
func setupEnhancedAuthEndpoints(
	router *gin.Engine,
	authHandlers *handlers.EnhancedAuthHandlers,
	logger *zap.Logger,
) {
	// API v1 group for enhanced authentication
	v1 := router.Group("/api/v1")
	
	// Enhanced authentication routes (public - no auth required)
	auth := v1.Group("/auth")
	{
		// Task 3.1: Enhanced login endpoint with Supabase integration
		auth.POST("/enhanced/login", authHandlers.EnhancedLogin)
		
		// Task 3.1: Enhanced logout endpoint with Supabase integration
		auth.POST("/enhanced/logout", authHandlers.EnhancedLogout)
		
		// Task 3.1: Enhanced token refresh endpoint with Supabase integration
		auth.POST("/enhanced/refresh", authHandlers.EnhancedRefreshToken)
		
		// Task 3.1: Enhanced get user endpoint with Supabase integration
		auth.GET("/enhanced/user", authHandlers.EnhancedGetUser)
	}

	// Enhanced authentication routes (alternative paths for compatibility)
	enhanced := v1.Group("/enhanced-auth")
	{
		// Alternative paths for enhanced authentication
		enhanced.POST("/login", authHandlers.EnhancedLogin)
		enhanced.POST("/logout", authHandlers.EnhancedLogout)
		enhanced.POST("/refresh", authHandlers.EnhancedRefreshToken)
		enhanced.GET("/user", authHandlers.EnhancedGetUser)
	}

	// Health check endpoints for enhanced authentication
	health := router.Group("/health")
	{
		health.GET("/enhanced-auth", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"status":  "healthy",
				"service": "enhanced-auth",
				"message": "Enhanced authentication service is operational",
			})
		})

		health.GET("/supabase-auth", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"status":  "healthy",
				"service": "supabase-auth",
				"message": "Supabase authentication service is configured",
				"url":     "https://lpxtghyvxuenyyisrrro.supabase.co",
			})
		})
	}

	logger.Info("✅ Enhanced authentication endpoints configured",
		zap.String("login_endpoint", "/api/v1/auth/enhanced/login"),
		zap.String("logout_endpoint", "/api/v1/auth/enhanced/logout"),
		zap.String("refresh_endpoint", "/api/v1/auth/enhanced/refresh"),
		zap.String("user_endpoint", "/api/v1/auth/enhanced/user"),
	)
}

// SetupEnhancedAuthMiddleware configures enhanced authentication middleware
// Task 3.1: Setup enhanced auth middleware for protected routes
func SetupEnhancedAuthMiddleware(
	router *gin.Engine,
	config *config.Config,
	logger *zap.Logger,
) error {
	// Create Supabase Auth Service for middleware
	supabaseAuthService, err := services.NewSupabaseAuthService(config, logger)
	if err != nil {
		logger.Error("Failed to create Supabase auth service for middleware", zap.Error(err))
		return err
	}

	// Create JWT service for middleware
	jwtService, err := sharedservices.NewJWTService(config)
	if err != nil {
		logger.Error("Failed to create JWT service for middleware", zap.Error(err))
		return err
	}

	// Setup protected routes with enhanced authentication middleware
	protected := router.Group("/api/v1/protected")
	protected.Use(EnhancedAuthMiddleware(supabaseAuthService, jwtService, config, logger))
	{
		// Example protected endpoints that require enhanced authentication
		protected.GET("/profile", func(c *gin.Context) {
			// Get user from context (set by middleware)
			userID, exists := c.Get("user_id")
			if !exists {
				c.JSON(401, gin.H{"error": "User not authenticated"})
				return
			}

			c.JSON(200, gin.H{
				"success": true,
				"message": "Profile retrieved successfully",
				"data": gin.H{
					"profile": gin.H{
						"user_id": userID,
						"preferences": gin.H{},
						"last_login": time.Now().Format(time.RFC3339),
					},
				},
			})
		})

		protected.GET("/dashboard", func(c *gin.Context) {
			// Get user from context (set by middleware)
			_, exists := c.Get("user_email")
			if !exists {
				c.JSON(401, gin.H{"error": "User not authenticated"})
				return
			}

			c.JSON(200, gin.H{
				"success": true,
				"message": "Dashboard data retrieved successfully",
				"data": gin.H{
					"dashboard": gin.H{
						"user_stats": gin.H{},
						"recent_activity": []interface{}{},
						"notifications": []interface{}{},
					},
				},
			})
		})
	}

	logger.Info("✅ Enhanced authentication middleware configured successfully")
	return nil
}

// EnhancedAuthMiddleware creates enhanced authentication middleware using Supabase
// Task 3.1: Create enhanced auth middleware with Supabase token validation
func EnhancedAuthMiddleware(
	supabaseAuthService *services.SupabaseAuthService,
	jwtService *sharedservices.JWTService,
	config *config.Config,
	logger *zap.Logger,
) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			logger.Warn("Enhanced Auth Middleware: No authorization header provided")
			c.JSON(401, gin.H{
				"error":   "Authorization header required",
				"code":    "AUTH_HEADER_MISSING",
				"message": "Please provide a valid authorization token",
			})
			c.Abort()
			return
		}

		// Extract Bearer token
		if len(authHeader) < 7 || authHeader[:7] != "Bearer " {
			logger.Warn("Enhanced Auth Middleware: Invalid authorization header format")
			c.JSON(401, gin.H{
				"error":   "Invalid authorization header format",
				"code":    "AUTH_HEADER_INVALID",
				"message": "Authorization header must be in format: Bearer <token>",
			})
			c.Abort()
			return
		}

		accessToken := authHeader[7:]

		// Validate token with Supabase
		user, err := supabaseAuthService.GetUser(c.Request.Context(), accessToken)
		if err != nil {
			logger.Warn("Enhanced Auth Middleware: Token validation failed",
				zap.Error(err),
				zap.String("client_ip", c.ClientIP()),
			)
			c.JSON(401, gin.H{
				"error":   "Invalid or expired token",
				"code":    "TOKEN_VALIDATION_FAILED",
				"message": "Please login again",
			})
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", user.ID)
		c.Set("user_email", user.Email)
		c.Set("user_created_at", user.CreatedAt)
		c.Set("user_updated_at", user.UpdatedAt)
		c.Set("auth_provider", "supabase")
		c.Set("token_type", "enhanced")

		logger.Debug("Enhanced Auth Middleware: Token validated successfully",
			zap.String("user_id", user.ID),
			zap.String("user_email", user.Email),
		)

		c.Next()
	}
}

// SetupEnhancedAuthHealthChecks configures health checks for enhanced authentication
// Task 3.1: Setup health checks for Supabase auth service
func SetupEnhancedAuthHealthChecks(
	router *gin.Engine,
	config *config.Config,
	logger *zap.Logger,
) error {
	// Create Supabase Auth Service for health checks
	supabaseAuthService, err := services.NewSupabaseAuthService(config, logger)
	if err != nil {
		logger.Error("Failed to create Supabase auth service for health checks", zap.Error(err))
		return err
	}

	// Health check endpoints
	health := router.Group("/health")
	{
		// Enhanced authentication health check
		health.GET("/enhanced-auth", func(c *gin.Context) {
			// Simple health check - verify service is initialized
			if supabaseAuthService == nil {
				c.JSON(503, gin.H{
					"status":  "unhealthy",
					"service": "enhanced-auth",
					"error":   "Supabase auth service not initialized",
				})
				return
			}

			c.JSON(200, gin.H{
				"status":  "healthy",
				"service": "enhanced-auth",
				"message": "Enhanced authentication service is operational",
			})
		})

		// Supabase connectivity health check
		health.GET("/supabase-auth", func(c *gin.Context) {
			// This would typically test actual connectivity to Supabase
			// For now, we just verify the service is configured
			c.JSON(200, gin.H{
				"status":  "healthy",
				"service": "supabase-auth",
				"message": "Supabase authentication service is configured",
				"url":     config.Supabase.URL,
			})
		})
	}

	logger.Info("✅ Enhanced authentication health checks configured successfully")
	return nil
}

// GetEnhancedAuthServiceInfo returns information about the enhanced auth service
// Task 3.1: Provide service information for monitoring and debugging
func GetEnhancedAuthServiceInfo(config *config.Config) map[string]interface{} {
	return map[string]interface{}{
		"service_name":    "enhanced-authentication",
		"version":         "1.0.0",
		"supabase_url":    config.Supabase.URL,
		"supabase_configured": config.Supabase.URL != "" && config.Supabase.AnonKey != "",
		"endpoints": map[string]string{
			"login":   "/api/v1/auth/enhanced/login",
			"logout":  "/api/v1/auth/enhanced/logout",
			"refresh": "/api/v1/auth/enhanced/refresh",
			"user":    "/api/v1/auth/enhanced/user",
		},
		"middleware_endpoints": map[string]string{
			"protected_profile":   "/api/v1/protected/profile",
			"protected_dashboard": "/api/v1/protected/dashboard",
		},
		"health_checks": map[string]string{
			"enhanced_auth": "/health/enhanced-auth",
			"supabase_auth": "/health/supabase-auth",
		},
	}
}
