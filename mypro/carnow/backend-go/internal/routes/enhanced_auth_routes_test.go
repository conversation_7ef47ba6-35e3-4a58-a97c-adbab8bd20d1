package routes

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"carnow-backend/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

// =============================================================================
// Task 3.1: Enhanced Authentication Routes Tests
// =============================================================================

// createTestConfig creates a test configuration for enhanced auth routes
func createTestConfig() *config.Config {
	return &config.Config{
		Supabase: config.SupabaseConfig{
			URL:            "https://test-project.supabase.co",
			Anon<PERSON>ey:        "test-anon-key-for-testing-purposes-only",
			ServiceRoleKey: "test-service-role-key-for-testing-purposes-only",
			JWTSecret:      "test-jwt-secret-for-testing-purposes-only",
			ProjectRef:     "test-project",
		},
		JWT: config.JWTConfig{
			Secret: "test-jwt-secret-key-for-testing-purposes-only",
		},
		Google: config.GoogleConfig{
			ClientID:     "test-google-client-id",
			ClientSecret: "test-google-client-secret",
		},
	}
}

// createTestLogger creates a test logger
func createTestLogger() *zap.Logger {
	logger, _ := zap.NewDevelopment()
	return logger
}

// TestSetupEnhancedAuthRoutes tests the setup of enhanced auth routes
func TestSetupEnhancedAuthRoutes(t *testing.T) {
	// Skip this test as it requires actual Supabase connection
	t.Skip("Skipping integration test - requires Supabase connection")

	gin.SetMode(gin.TestMode)
	router := gin.New()
	config := createTestConfig()
	logger := createTestLogger()

	// Setup enhanced auth routes
	err := SetupEnhancedAuthRoutes(router, config, logger)
	assert.NoError(t, err)

	// Test that routes are registered
	routes := router.Routes()
	assert.NotEmpty(t, routes)

	// Check for specific enhanced auth routes
	foundLoginRoute := false
	foundLogoutRoute := false
	foundRefreshRoute := false
	foundUserRoute := false

	for _, route := range routes {
		switch route.Path {
		case "/api/v1/auth/enhanced/login":
			foundLoginRoute = true
		case "/api/v1/auth/enhanced/logout":
			foundLogoutRoute = true
		case "/api/v1/auth/enhanced/refresh":
			foundRefreshRoute = true
		case "/api/v1/auth/enhanced/user":
			foundUserRoute = true
		}
	}

	assert.True(t, foundLoginRoute, "Enhanced login route should be registered")
	assert.True(t, foundLogoutRoute, "Enhanced logout route should be registered")
	assert.True(t, foundRefreshRoute, "Enhanced refresh route should be registered")
	assert.True(t, foundUserRoute, "Enhanced user route should be registered")
}

// TestSetupEnhancedAuthHealthChecks tests the setup of enhanced auth health checks
func TestSetupEnhancedAuthHealthChecks(t *testing.T) {
	// Skip this test as it requires actual Supabase connection
	t.Skip("Skipping integration test - requires Supabase connection")

	gin.SetMode(gin.TestMode)
	router := gin.New()
	config := createTestConfig()
	logger := createTestLogger()

	// Setup enhanced auth health checks
	err := SetupEnhancedAuthHealthChecks(router, config, logger)
	assert.NoError(t, err)

	// Test enhanced auth health check endpoint
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/health/enhanced-auth", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "healthy", response["status"])
	assert.Equal(t, "enhanced-auth", response["service"])
}

// TestGetEnhancedAuthServiceInfo tests the service info function
func TestGetEnhancedAuthServiceInfo(t *testing.T) {
	config := createTestConfig()

	info := GetEnhancedAuthServiceInfo(config)

	assert.NotNil(t, info)
	assert.Equal(t, "enhanced-authentication", info["service_name"])
	assert.Equal(t, "1.0.0", info["version"])
	assert.Equal(t, config.Supabase.URL, info["supabase_url"])
	assert.True(t, info["supabase_configured"].(bool))

	// Check endpoints
	endpoints, ok := info["endpoints"].(map[string]string)
	assert.True(t, ok)
	assert.Equal(t, "/api/v1/auth/enhanced/login", endpoints["login"])
	assert.Equal(t, "/api/v1/auth/enhanced/logout", endpoints["logout"])
	assert.Equal(t, "/api/v1/auth/enhanced/refresh", endpoints["refresh"])
	assert.Equal(t, "/api/v1/auth/enhanced/user", endpoints["user"])

	// Check middleware endpoints
	middlewareEndpoints, ok := info["middleware_endpoints"].(map[string]string)
	assert.True(t, ok)
	assert.Equal(t, "/api/v1/protected/profile", middlewareEndpoints["protected_profile"])
	assert.Equal(t, "/api/v1/protected/dashboard", middlewareEndpoints["protected_dashboard"])

	// Check health checks
	healthChecks, ok := info["health_checks"].(map[string]string)
	assert.True(t, ok)
	assert.Equal(t, "/health/enhanced-auth", healthChecks["enhanced_auth"])
	assert.Equal(t, "/health/supabase-auth", healthChecks["supabase_auth"])
}

// TestEnhancedAuthMiddleware_MissingAuthHeader tests middleware with missing auth header
func TestEnhancedAuthMiddleware_MissingAuthHeader(t *testing.T) {
	// Skip this test as it requires actual Supabase connection
	t.Skip("Skipping integration test - requires Supabase connection")

	gin.SetMode(gin.TestMode)
	router := gin.New()
	config := createTestConfig()
	logger := createTestLogger()

	// Setup enhanced auth middleware
	err := SetupEnhancedAuthMiddleware(router, config, logger)
	assert.NoError(t, err)

	// Test protected endpoint without auth header
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/protected/profile", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusUnauthorized, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "AUTH_HEADER_MISSING", response["code"])
}

// TestEnhancedAuthMiddleware_InvalidAuthHeader tests middleware with invalid auth header
func TestEnhancedAuthMiddleware_InvalidAuthHeader(t *testing.T) {
	// Skip this test as it requires actual Supabase connection
	t.Skip("Skipping integration test - requires Supabase connection")

	gin.SetMode(gin.TestMode)
	router := gin.New()
	config := createTestConfig()
	logger := createTestLogger()

	// Setup enhanced auth middleware
	err := SetupEnhancedAuthMiddleware(router, config, logger)
	assert.NoError(t, err)

	// Test protected endpoint with invalid auth header
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/protected/profile", nil)
	req.Header.Set("Authorization", "Invalid token")
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusUnauthorized, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "AUTH_HEADER_INVALID", response["code"])
}

// TestEnhancedAuthRoutes_Integration tests the integration of enhanced auth routes
func TestEnhancedAuthRoutes_Integration(t *testing.T) {
	// Skip this test as it requires actual Supabase connection
	t.Skip("Skipping integration test - requires Supabase connection")

	gin.SetMode(gin.TestMode)
	router := gin.New()
	config := createTestConfig()
	logger := createTestLogger()

	// Setup all enhanced auth components
	err := SetupEnhancedAuthRoutes(router, config, logger)
	assert.NoError(t, err)

	err = SetupEnhancedAuthMiddleware(router, config, logger)
	assert.NoError(t, err)

	err = SetupEnhancedAuthHealthChecks(router, config, logger)
	assert.NoError(t, err)

	// Test login endpoint (should return error without valid credentials)
	loginPayload := map[string]string{
		"email":    "<EMAIL>",
		"password": "password123",
	}
	jsonPayload, _ := json.Marshal(loginPayload)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v1/auth/enhanced/login", bytes.NewBuffer(jsonPayload))
	req.Header.Set("Content-Type", "application/json")
	router.ServeHTTP(w, req)

	// Should return error since we don't have real Supabase credentials
	assert.NotEqual(t, http.StatusOK, w.Code)

	// Test health check endpoints
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("GET", "/health/enhanced-auth", nil)
	router.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	w = httptest.NewRecorder()
	req, _ = http.NewRequest("GET", "/health/supabase-auth", nil)
	router.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
}

// TestEnhancedAuthRoutes_ConfigValidation tests configuration validation
func TestEnhancedAuthRoutes_ConfigValidation(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	logger := createTestLogger()

	// Test with invalid configuration (missing Supabase URL)
	invalidConfig := &config.Config{
		Supabase: config.SupabaseConfig{
			// URL missing
			AnonKey:        "test-anon-key",
			ServiceRoleKey: "test-service-role-key",
			JWTSecret:      "test-jwt-secret",
			ProjectRef:     "test-project",
		},
		JWT: config.JWTConfig{
			Secret: "test-jwt-secret-key-for-testing-purposes-only",
		},
	}

	// Setup should fail with invalid configuration
	err := SetupEnhancedAuthRoutes(router, invalidConfig, logger)
	assert.Error(t, err)
}

// BenchmarkEnhancedAuthServiceInfo benchmarks the service info function
func BenchmarkEnhancedAuthServiceInfo(b *testing.B) {
	config := createTestConfig()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = GetEnhancedAuthServiceInfo(config)
	}
}

// TestEnhancedAuthRoutes_AlternativePaths tests alternative route paths
func TestEnhancedAuthRoutes_AlternativePaths(t *testing.T) {
	// Skip this test as it requires actual Supabase connection
	t.Skip("Skipping integration test - requires Supabase connection")

	gin.SetMode(gin.TestMode)
	router := gin.New()
	config := createTestConfig()
	logger := createTestLogger()

	// Setup enhanced auth routes
	err := SetupEnhancedAuthRoutes(router, config, logger)
	assert.NoError(t, err)

	// Test alternative paths
	routes := router.Routes()
	
	foundAlternativeLogin := false
	foundAlternativeLogout := false
	foundAlternativeRefresh := false
	foundAlternativeUser := false

	for _, route := range routes {
		switch route.Path {
		case "/enhanced-auth/login":
			foundAlternativeLogin = true
		case "/enhanced-auth/logout":
			foundAlternativeLogout = true
		case "/enhanced-auth/refresh":
			foundAlternativeRefresh = true
		case "/enhanced-auth/user":
			foundAlternativeUser = true
		}
	}

	assert.True(t, foundAlternativeLogin, "Alternative login route should be registered")
	assert.True(t, foundAlternativeLogout, "Alternative logout route should be registered")
	assert.True(t, foundAlternativeRefresh, "Alternative refresh route should be registered")
	assert.True(t, foundAlternativeUser, "Alternative user route should be registered")
}
