# Legacy System Cleanup Plan

## Overview
This plan outlines the gradual removal of the old authentication system to prevent conflicts with the new Enhanced Authentication system.

## Current Status

### Old System (To be removed)
- `SimpleJWTMiddleware` in `backend-go/internal/shared/middleware/jwt_middleware.go`
- `LegacyJWTMiddleware` in `backend-go/internal/middleware/enhanced_jwt_middleware.go`
- Old JWT handlers in `backend-go/internal/handlers/jwt_middleware.go`
- Old routes in `backend-go/internal/routes/secure_routes.go`

### New System (To keep)
- `EnhancedSimpleJWTMiddleware` in `backend-go/internal/shared/middleware/jwt_middleware.go`
- `SupabaseAuthService` in `backend-go/internal/services/supabase_auth_service.go`
- `EnhancedAuthHandlers` in `backend-go/internal/handlers/enhanced_auth_handlers.go`
- `SetupEnhancedAuthRoutes` in `backend-go/internal/routes/enhanced_auth_routes.go`

## Cleanup Steps

### Phase 1: Mark Old Code as Deprecated
1. Add deprecation comments to old functions
2. Add warnings in logs when old code is used
3. Create migration guide for developers

### Phase 2: Remove Old Routes
1. Remove old authentication routes
2. Update route configuration to use new system only
3. Test that all functionality still works

### Phase 3: Remove Old Middleware
1. Remove old middleware functions
2. Update all route configurations
3. Ensure no breaking changes

### Phase 4: Clean Up Old Handlers
1. Remove old handler files
2. Update imports and dependencies
3. Remove unused code

## Migration Timeline
- **Week 1**: Mark old code as deprecated
- **Week 2**: Remove old routes
- **Week 3**: Remove old middleware
- **Week 4**: Clean up old handlers

## Success Criteria
- ✅ No conflicts between old and new systems
- ✅ All functionality preserved
- ✅ Clean, maintainable codebase
- ✅ Forever Plan compliance maintained 