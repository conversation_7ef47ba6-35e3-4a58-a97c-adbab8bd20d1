package middleware

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"carnow-backend/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
)

// MockDatabaseInterface implements DatabaseInterface for testing
type MockDatabaseInterface struct{}

func (m *MockDatabaseInterface) QueryRow(ctx context.Context, query string, args ...interface{}) pgx.Row {
	return nil
}

func (m *MockDatabaseInterface) Exec(ctx context.Context, query string, args ...interface{}) error {
	return nil
}

// TestExtractTokenWithValidation tests the token extraction function
func TestExtractTokenWithValidation(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		authHeader     string
		expectedToken  string
		shouldBeEmpty  bool
	}{
		{
			name:           "Valid Bearer Token",
			authHeader:     "Bearer valid_token_123",
			expectedToken:  "valid_token_123",
			shouldBeEmpty:  false,
		},
		{
			name:           "Missing Authorization Header",
			authHeader:     "",
			expectedToken:  "",
			shouldBeEmpty:  true,
		},
		{
			name:           "Invalid Format - No Bearer",
			authHeader:     "Token valid_token_123",
			expectedToken:  "",
			shouldBeEmpty:  true,
		},
		{
			name:           "Invalid Format - No Token",
			authHeader:     "Bearer",
			expectedToken:  "",
			shouldBeEmpty:  true,
		},
		{
			name:           "Invalid Format - Empty Token",
			authHeader:     "Bearer ",
			expectedToken:  "",
			shouldBeEmpty:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("GET", "/test", nil)
			
			if tt.authHeader != "" {
				c.Request.Header.Set("Authorization", tt.authHeader)
			}

			// Test token extraction
			token := extractTokenWithValidation(c)

			if tt.shouldBeEmpty {
				assert.Empty(t, token, "Expected empty token")
			} else {
				assert.Equal(t, tt.expectedToken, token, "Token should match expected value")
			}
		})
	}
}

// TestValidateRealDataOnly tests the real data validation function
func TestValidateRealDataOnly(t *testing.T) {
	tests := []struct {
		name        string
		user        *SupabaseUser
		expectError bool
		errorMsg    string
	}{
		{
			name: "Valid Real User",
			user: &SupabaseUser{
				ID:    "real_user_id_123",
				Email: "<EMAIL>",
				Role:  "authenticated",
			},
			expectError: false,
		},
		{
			name: "Empty User ID",
			user: &SupabaseUser{
				ID:    "",
				Email: "<EMAIL>",
				Role:  "authenticated",
			},
			expectError: true,
			errorMsg:    "user ID cannot be empty",
		},
		{
			name: "Mock User ID",
			user: &SupabaseUser{
				ID:    "mock_user_123",
				Email: "<EMAIL>",
				Role:  "authenticated",
			},
			expectError: true,
			errorMsg:    "mock data detected in user ID",
		},
		{
			name: "Test Email",
			user: &SupabaseUser{
				ID:    "real_user_id_123",
				Email: "<EMAIL>",
				Role:  "authenticated",
			},
			expectError: true,
			errorMsg:    "mock data detected in user email",
		},
		{
			name: "Invalid Email Format",
			user: &SupabaseUser{
				ID:    "real_user_id_123",
				Email: "invalid_email",
				Role:  "authenticated",
			},
			expectError: true,
			errorMsg:    "invalid email format",
		},
		{
			name: "Empty Role",
			user: &SupabaseUser{
				ID:    "real_user_id_123",
				Email: "<EMAIL>",
				Role:  "",
			},
			expectError: true,
			errorMsg:    "user role cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateRealDataOnly(tt.user)

			if tt.expectError {
				assert.Error(t, err, "Expected an error")
				assert.Contains(t, err.Error(), tt.errorMsg, "Error message should contain expected text")
			} else {
				assert.NoError(t, err, "Expected no error")
			}
		})
	}
}

// TestEnhancedSimpleJWTMiddleware tests the enhanced middleware with mock scenarios
func TestEnhancedSimpleJWTMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create mock config
	cfg := &config.Config{
		Supabase: config.SupabaseConfig{
			URL:     "https://test.supabase.co",
			AnonKey: "test_anon_key",
		},
	}

	// Create mock database
	mockDB := &MockDatabaseInterface{}

	tests := []struct {
		name           string
		authHeader     string
		expectedStatus int
		expectedCode   string
	}{
		{
			name:           "Missing Authorization Header",
			authHeader:     "",
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   "AUTH_HEADER_MISSING",
		},
		{
			name:           "Invalid Authorization Format",
			authHeader:     "Token invalid_format",
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   "AUTH_HEADER_MISSING",
		},
		{
			name:           "Empty Bearer Token",
			authHeader:     "Bearer ",
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   "AUTH_HEADER_MISSING",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("GET", "/test", nil)
			
			if tt.authHeader != "" {
				c.Request.Header.Set("Authorization", tt.authHeader)
			}

			// Create middleware
			middleware := EnhancedSimpleJWTMiddleware(cfg, mockDB)

			// Execute middleware
			middleware(c)

			// Check response
			assert.Equal(t, tt.expectedStatus, w.Code, "Status code should match")
			
			// Note: We can't test successful token validation without a real Supabase instance
			// This test focuses on the error handling and validation logic
		})
	}
}
