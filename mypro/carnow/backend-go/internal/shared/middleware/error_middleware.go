package middleware

import (
	"fmt"
	"net/http"
	"runtime"
	"strings"
	"time"

	"carnow-backend/internal/shared/errors"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ErrorMiddleware provides centralized error handling for the application
// Task 1.4: Comprehensive error handling middleware with standardized responses
type ErrorMiddleware struct {
	logger *zap.Logger
	config *ErrorConfig
}

// ErrorConfig contains configuration for error handling
type ErrorConfig struct {
	IncludeStackTrace bool   `json:"include_stack_trace"`
	Environment       string `json:"environment"`
	MaxErrorLength    int    `json:"max_error_length"`
	SanitizeErrors    bool   `json:"sanitize_errors"`
}

// NewErrorMiddleware creates a new error handling middleware
func NewErrorMiddleware(logger *zap.Logger, config *ErrorConfig) *ErrorMiddleware {
	if config == nil {
		config = &ErrorConfig{
			IncludeStackTrace: false,
			Environment:       "production",
			MaxErrorLength:    500,
			SanitizeErrors:    true,
		}
	}

	return &ErrorMiddleware{
		logger: logger,
		config: config,
	}
}

// Handler returns the Gin middleware function for error handling
// Task 1.4: Centralized error handling with proper logging and monitoring
func (em *ErrorMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Process the request
		c.Next()

		// Handle any errors that occurred during request processing
		if len(c.Errors) > 0 {
			em.handleErrors(c)
		}
	}
}

// RecoveryHandler provides panic recovery with proper error handling
// Task 1.4: Panic recovery with standardized error responses
func (em *ErrorMiddleware) RecoveryHandler() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		clientIP := c.ClientIP()
		userAgent := c.GetHeader("User-Agent")
		requestID := c.GetHeader("X-Request-ID")
		
		// Log the panic with stack trace
		stack := make([]byte, 4096)
		length := runtime.Stack(stack, false)
		
		em.logger.Error("Panic recovered",
			zap.String("client_ip", clientIP),
			zap.String("user_agent", userAgent),
			zap.String("request_id", requestID),
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.Any("panic", recovered),
			zap.String("stack", string(stack[:length])),
		)

		// Create internal error
		authErr := errors.ErrInternalError(fmt.Sprintf("Panic recovered: %v", recovered))
		authErr.WithContext(requestID, "", clientIP, userAgent)

		// Don't include stack trace in production
		if em.config.Environment != "production" && em.config.IncludeStackTrace {
			authErr.WithDetails(string(stack[:length]))
		}

		c.JSON(authErr.HTTPStatus, authErr.ToHTTPResponse())
		c.Abort()
	})
}

// handleErrors processes accumulated errors from the request
func (em *ErrorMiddleware) handleErrors(c *gin.Context) {
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")
	requestID := c.GetHeader("X-Request-ID")

	// Get the last error (most recent)
	lastError := c.Errors.Last()
	if lastError == nil {
		return
	}

	// Check if it's already an AuthError
	if authErr, ok := lastError.Err.(*errors.AuthError); ok {
		// Add context if not already set
		if authErr.RequestID == "" {
			authErr.WithContext(requestID, "", clientIP, userAgent)
		}

		em.logger.Error("Authentication error",
			zap.String("error_code", string(authErr.Code)),
			zap.String("error_message", authErr.Message),
			zap.String("client_ip", clientIP),
			zap.String("request_id", requestID),
			zap.String("path", c.Request.URL.Path),
			zap.String("method", c.Request.Method),
		)

		c.JSON(authErr.HTTPStatus, authErr.ToHTTPResponse())
		return
	}

	// Convert regular error to AuthError
	authErr := em.convertToAuthError(lastError.Err)
	authErr.WithContext(requestID, "", clientIP, userAgent)

	em.logger.Error("Request error",
		zap.String("error_code", string(authErr.Code)),
		zap.String("error_message", authErr.Message),
		zap.String("client_ip", clientIP),
		zap.String("request_id", requestID),
		zap.String("path", c.Request.URL.Path),
		zap.String("method", c.Request.Method),
		zap.Error(lastError.Err),
	)

	c.JSON(authErr.HTTPStatus, authErr.ToHTTPResponse())
}

// convertToAuthError converts a regular error to an AuthError
// Task 1.4: Smart error conversion with proper categorization
func (em *ErrorMiddleware) convertToAuthError(err error) *errors.AuthError {
	errorMsg := err.Error()
	lowerMsg := strings.ToLower(errorMsg)

	// Categorize errors based on content
	switch {
	case strings.Contains(lowerMsg, "token"):
		if strings.Contains(lowerMsg, "expired") {
			return errors.ErrTokenExpired()
		} else if strings.Contains(lowerMsg, "invalid") {
			return errors.ErrTokenInvalid(errorMsg)
		} else if strings.Contains(lowerMsg, "missing") {
			return errors.ErrTokenMissing()
		}
		return errors.NewAuthError(errors.ErrCodeTokenInvalid, "Token error", errorMsg)

	case strings.Contains(lowerMsg, "user not found"):
		return errors.ErrUserNotFound()

	case strings.Contains(lowerMsg, "access denied") || strings.Contains(lowerMsg, "forbidden"):
		return errors.ErrAccessDenied("resource")

	case strings.Contains(lowerMsg, "rate limit") || strings.Contains(lowerMsg, "too many"):
		return errors.ErrRateLimitExceeded(time.Minute)

	case strings.Contains(lowerMsg, "mock") || strings.Contains(lowerMsg, "test") || strings.Contains(lowerMsg, "fake"):
		return errors.ErrMockDataDetected("data")

	case strings.Contains(lowerMsg, "validation") || strings.Contains(lowerMsg, "invalid input"):
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "Validation failed", errorMsg)

	case strings.Contains(lowerMsg, "database") || strings.Contains(lowerMsg, "connection"):
		return errors.NewAuthError(errors.ErrCodeDatabaseError, "Database error", em.sanitizeError(errorMsg))

	case strings.Contains(lowerMsg, "network") || strings.Contains(lowerMsg, "timeout"):
		return errors.NewAuthError(errors.ErrCodeNetworkError, "Network error", em.sanitizeError(errorMsg))

	case strings.Contains(lowerMsg, "service unavailable") || strings.Contains(lowerMsg, "temporarily unavailable"):
		return errors.ErrServiceUnavailable("service")

	default:
		return errors.ErrInternalError(em.sanitizeError(errorMsg))
	}
}

// sanitizeError removes sensitive information from error messages
func (em *ErrorMiddleware) sanitizeError(errorMsg string) string {
	if !em.config.SanitizeErrors {
		return errorMsg
	}

	// Remove sensitive patterns
	sensitivePatterns := []string{
		"password",
		"secret",
		"key",
		"token",
		"credential",
		"auth",
	}

	sanitized := errorMsg
	for _, pattern := range sensitivePatterns {
		if strings.Contains(strings.ToLower(sanitized), pattern) {
			sanitized = "Sensitive information removed from error message"
			break
		}
	}

	// Limit error length
	if len(sanitized) > em.config.MaxErrorLength {
		sanitized = sanitized[:em.config.MaxErrorLength] + "..."
	}

	return sanitized
}

// ValidationErrorHandler handles validation errors specifically
// Task 1.4: Specialized handler for validation errors
func (em *ErrorMiddleware) ValidationErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Check for validation errors
		for _, ginErr := range c.Errors {
			if ginErr.Type == gin.ErrorTypeBind {
				clientIP := c.ClientIP()
				userAgent := c.GetHeader("User-Agent")
				requestID := c.GetHeader("X-Request-ID")

				authErr := errors.NewAuthError(
					errors.ErrCodeValidationFailed,
					"Request validation failed",
					ginErr.Error(),
				)
				authErr.WithContext(requestID, "", clientIP, userAgent)

				em.logger.Warn("Validation error",
					zap.String("error", ginErr.Error()),
					zap.String("client_ip", clientIP),
					zap.String("request_id", requestID),
				)

				c.JSON(authErr.HTTPStatus, authErr.ToHTTPResponse())
				c.Abort()
				return
			}
		}
	}
}

// NotFoundHandler handles 404 errors
// Task 1.4: Standardized 404 error handling
func (em *ErrorMiddleware) NotFoundHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		userAgent := c.GetHeader("User-Agent")
		requestID := c.GetHeader("X-Request-ID")

		authErr := errors.NewAuthError(
			errors.ErrCodeUserNotFound,
			"Endpoint not found",
			fmt.Sprintf("The requested endpoint %s %s was not found", c.Request.Method, c.Request.URL.Path),
		)
		authErr.WithContext(requestID, "", clientIP, userAgent)

		em.logger.Info("Endpoint not found",
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.String("client_ip", clientIP),
			zap.String("request_id", requestID),
		)

		c.JSON(http.StatusNotFound, authErr.ToHTTPResponse())
	}
}

// MethodNotAllowedHandler handles 405 errors
// Task 1.4: Standardized 405 error handling
func (em *ErrorMiddleware) MethodNotAllowedHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		userAgent := c.GetHeader("User-Agent")
		requestID := c.GetHeader("X-Request-ID")

		authErr := errors.NewAuthError(
			errors.ErrCodeValidationFailed,
			"Method not allowed",
			fmt.Sprintf("The method %s is not allowed for endpoint %s", c.Request.Method, c.Request.URL.Path),
		)
		authErr.WithContext(requestID, "", clientIP, userAgent)

		em.logger.Info("Method not allowed",
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.String("client_ip", clientIP),
			zap.String("request_id", requestID),
		)

		c.JSON(http.StatusMethodNotAllowed, authErr.ToHTTPResponse())
	}
}
