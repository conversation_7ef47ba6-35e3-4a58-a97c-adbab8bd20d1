package middleware

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/shared/errors"

	"github.com/gin-gonic/gin"
	"github.com/jackc/pgx/v5"
)

// SupabaseUser represents user data from Supabase Auth API
type SupabaseUser struct {
	ID    string `json:"id"`
	Email string `json:"email"`
	Role  string `json:"role"`
	Aud   string `json:"aud,omitempty"`
}

// DatabaseInterface defines the interface for database operations needed by middleware
type DatabaseInterface interface {
	QueryRow(ctx context.Context, query string, args ...interface{}) pgx.Row
	Exec(ctx context.Context, query string, args ...interface{}) error
	// Add other methods as needed
}

// EnhancedSimpleJWTMiddleware validates JWT tokens with improved error handling and logging
// Task 1.1: Enhanced version of SimpleJWTMiddleware with better error handling and logging
// This follows the Forever Plan: simple architecture with enhanced security and REAL DATA ONLY
func EnhancedSimpleJWTMiddleware(cfg *config.Config, db DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()
		clientIP := c.ClientIP()
		userAgent := c.GetHeader("User-Agent")

		log.Printf("🔍 Enhanced JWT Middleware: Processing request from %s (User-Agent: %s)", clientIP, userAgent)

		// Extract token with improved validation
		token := extractTokenWithValidation(c)
		if token == "" {
			log.Printf("❌ Enhanced JWT Middleware: No valid token provided from %s", clientIP)
			handleAuthError(c, fmt.Errorf("authorization header missing or invalid format"))
			return
		}

		log.Printf("🔍 Enhanced JWT Middleware: Validating token (length: %d) from %s", len(token), clientIP)

		// Validate with Supabase (REAL DATA ONLY)
		user, err := validateTokenWithSupabase(cfg, token)
		if err != nil {
			log.Printf("❌ Enhanced JWT Middleware: Token validation failed for %s: %v", clientIP, err)
			handleAuthError(c, err)
			return
		}

		// Validate real data only (Forever Plan compliance)
		if err := validateRealDataOnly(user); err != nil {
			log.Printf("❌ Enhanced JWT Middleware: Real data validation failed for %s: %v", clientIP, err)
			handleAuthError(c, fmt.Errorf("invalid user data format"))
			return
		}

		// Set user context with timestamp (REAL DATA FROM SUPABASE)
		authTimestamp := time.Now()
		c.Set("user_id", user.ID)
		c.Set("user_email", user.Email)
		c.Set("user_role", user.Role)
		c.Set("auth_source", "supabase")
		c.Set("auth_timestamp", authTimestamp)
		c.Set("client_ip", clientIP)

		// Auto-create user if doesn't exist (using pgx)
		if err := ensureUserExistsWithPgx(db, user); err != nil {
			log.Printf("❌ Enhanced JWT Middleware: Failed to ensure user exists for %s: %v", clientIP, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":     "Failed to process user information",
				"code":      "USER_PROCESSING_ERROR",
				"message":   "Internal server error",
				"timestamp": authTimestamp.Format(time.RFC3339),
			})
			c.Abort()
			return
		}

		processingTime := time.Since(startTime)
		log.Printf("✅ Enhanced JWT Middleware: User %s authenticated successfully from %s (processing time: %v)",
			user.Email, clientIP, processingTime)

		c.Next()
	}
}

// DEPRECATED: This function is deprecated and will be removed in the next version.
// Use EnhancedSimpleJWTMiddleware instead for better security and Forever Plan compliance.
// This follows the Forever Plan: simple architecture with enhanced security
func SimpleJWTMiddleware(cfg *config.Config, db DatabaseInterface) gin.HandlerFunc {
	log.Printf("⚠️ WARNING: SimpleJWTMiddleware is deprecated. Use EnhancedSimpleJWTMiddleware instead.")

	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			log.Println("❌ JWT Middleware: No authorization header")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Authorization header required",
				"code":    "MISSING_AUTH_HEADER",
				"message": "Please provide a valid Bearer token",
			})
			c.Abort()
			return
		}

		// Extract Bearer token
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			log.Println("❌ JWT Middleware: Invalid authorization header format")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid authorization header format",
				"code":    "INVALID_AUTH_FORMAT",
				"message": "Authorization header must be in format: Bearer <token>",
			})
			c.Abort()
			return
		}

		tokenString := tokenParts[1]
		log.Printf("🔍 JWT Middleware: Validating token (length: %d)", len(tokenString))

		// Try Supabase validation (for Google OAuth tokens)
		user, err := validateTokenWithSupabase(cfg, tokenString)
		if err != nil {
			log.Printf("❌ JWT Middleware: Token validation failed: %v", err)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid token",
				"code":    "TOKEN_VALIDATION_FAILED",
				"message": "Token validation failed",
			})
			c.Abort()
			return
		}

		// Use Supabase user data
		log.Printf("✅ JWT Middleware: Token validated for user: %s (%s)", user.Email, user.ID)

		// Set user information in context
		c.Set("user_id", user.ID)
		c.Set("user_email", user.Email)
		c.Set("user_role", user.Role)
		c.Set("token_type", "supabase")

		// Auto-create user if doesn't exist (using pgx)
		if err := ensureUserExistsWithPgx(db, user); err != nil {
			log.Printf("⚠️ JWT Middleware: Failed to ensure user exists: %v", err)
			// Continue anyway - user might be created later
		}

		c.Next()
	}
}

// OptionalJWTMiddleware allows both authenticated and non-authenticated requests
func OptionalJWTMiddleware(cfg *config.Config, db DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")

		// If no auth header, continue without setting user context
		if authHeader == "" {
			log.Println("🔓 Optional JWT Middleware: No auth header, continuing without authentication")
			c.Next()
			return
		}

		// If auth header exists, validate it
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			log.Println("⚠️ Optional JWT Middleware: Invalid auth format, continuing without authentication")
			c.Next()
			return
		}

		tokenString := tokenParts[1]
		user, err := validateTokenWithSupabase(cfg, tokenString)

		// If valid token, set user context
		if err == nil {
			log.Printf("✅ Optional JWT Middleware: Token validated for user: %s", user.Email)

			// Auto-create user if doesn't exist
			_ = ensureUserExistsWithPgx(db, user)

			c.Set("user_id", user.ID)
			c.Set("user_email", user.Email)
			c.Set("user_role", user.Role)
		} else {
			log.Printf("⚠️ Optional JWT Middleware: Token validation failed: %v", err)
		}

		c.Next()
	}
}

// validateTokenWithSupabase validates JWT token by calling Supabase Auth API
func validateTokenWithSupabase(cfg *config.Config, token string) (*SupabaseUser, error) {
	// Create request to Supabase Auth API
	url := fmt.Sprintf("%s/auth/v1/user", cfg.Supabase.URL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("apikey", cfg.Supabase.AnonKey)

	// Make request with timeout
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to validate token: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("invalid token - status: %d", resp.StatusCode)
	}

	// Parse response
	var supabaseUser SupabaseUser
	if err := json.NewDecoder(resp.Body).Decode(&supabaseUser); err != nil {
		return nil, fmt.Errorf("failed to parse user data: %v", err)
	}

	// Set default role if not provided
	if supabaseUser.Role == "" {
		supabaseUser.Role = "authenticated"
	}

	return &supabaseUser, nil
}

// extractTokenWithValidation extracts and validates Bearer token from Authorization header
// Task 1.1: Improved token extraction with validation
func extractTokenWithValidation(c *gin.Context) string {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		return ""
	}

	tokenParts := strings.Split(authHeader, " ")
	if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
		return ""
	}

	token := strings.TrimSpace(tokenParts[1])
	if token == "" {
		return ""
	}

	return token
}

// handleAuthError handles authentication errors with enhanced error responses
// Task 1.4: Enhanced error handling using standardized error system
func handleAuthError(c *gin.Context, err error) {
	errorMsg := err.Error()
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")
	requestID := c.GetHeader("X-Request-ID")

	log.Printf("❌ Auth Error from %s: %v", clientIP, err)

	var authErr *errors.AuthError

	// Map error types to standardized auth errors
	switch {
	case strings.Contains(errorMsg, "expired"):
		authErr = errors.ErrTokenExpired()
	case strings.Contains(errorMsg, "invalid"):
		authErr = errors.ErrTokenInvalid(errorMsg)
	case strings.Contains(errorMsg, "missing") || strings.Contains(errorMsg, "authorization header"):
		authErr = errors.ErrTokenMissing()
	case strings.Contains(errorMsg, "mock") || strings.Contains(errorMsg, "test") || strings.Contains(errorMsg, "fake"):
		authErr = errors.ErrMockDataDetected("token")
	case strings.Contains(errorMsg, "user not found"):
		authErr = errors.ErrUserNotFound()
	case strings.Contains(errorMsg, "access denied"):
		authErr = errors.ErrAccessDenied("resource")
	default:
		authErr = errors.NewAuthError(errors.ErrCodeAuthFailed, "Authentication failed", errorMsg)
	}

	// Add context information
	authErr.WithContext(requestID, "", clientIP, userAgent)

	// Send standardized error response
	c.JSON(authErr.HTTPStatus, authErr.ToHTTPResponse())
	c.Abort()
}

// validateRealDataOnly validates that user data is real and from Supabase
// Task 1.1: Forever Plan compliance - ensure real data only
func validateRealDataOnly(user *SupabaseUser) error {
	// Check for real Supabase user ID format
	if user.ID == "" {
		return fmt.Errorf("user ID cannot be empty")
	}

	// Check for mock data patterns (Forever Plan compliance)
	mockPatterns := []string{"mock_", "test_", "fake_", "sample_", "demo_"}
	for _, pattern := range mockPatterns {
		if strings.Contains(strings.ToLower(user.ID), pattern) {
			return fmt.Errorf("mock data detected in user ID: %s", user.ID)
		}
		if strings.Contains(strings.ToLower(user.Email), pattern) {
			return fmt.Errorf("mock data detected in user email: %s", user.Email)
		}
	}

	// Validate real email format
	if user.Email == "" || !strings.Contains(user.Email, "@") {
		return fmt.Errorf("invalid email format: %s", user.Email)
	}

	// Validate role is set
	if user.Role == "" {
		return fmt.Errorf("user role cannot be empty")
	}

	return nil
}

// ensureUserExistsWithPgx checks if user exists in Go backend, creates if not (using pgx)
// This follows the Forever Plan: simple database operations with pgx only
func ensureUserExistsWithPgx(db DatabaseInterface, user *SupabaseUser) error {
	log.Printf("🔍 Auth: Checking user existence for: %s (%s)", user.Email, user.ID)

	// For Google OAuth users, the ID is not a UUID but a Google user ID
	// We'll skip the UUID validation and database operations for now
	// since the user is already authenticated with Supabase

	log.Printf("✅ Auth: User authenticated with Supabase: %s (%s)", user.Email, user.ID)
	log.Printf("✅ Auth: Skipping database operations for Google OAuth user")

	return nil
}

// GetUserIDFromContext extracts user ID from Gin context
func GetUserIDFromContext(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return ""
}
