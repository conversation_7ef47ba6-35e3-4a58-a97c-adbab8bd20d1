package middleware

import (
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	autherrors "carnow-backend/internal/shared/errors"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

// setupTestRouter creates a test router with error middleware
func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)

	logger, _ := zap.NewDevelopment()
	config := &ErrorConfig{
		IncludeStackTrace: false,
		Environment:       "test",
		MaxErrorLength:    500,
		SanitizeErrors:    true,
	}

	errorMiddleware := NewErrorMiddleware(logger, config)

	router := gin.New()
	router.Use(errorMiddleware.Handler())
	router.Use(errorMiddleware.RecoveryHandler())

	return router
}

// TestErrorMiddlewareCreation tests creating error middleware
// Task 1.4: Test error middleware initialization
func TestErrorMiddlewareCreation(t *testing.T) {
	logger, _ := zap.NewDevelopment()

	t.Run("With Config", func(t *testing.T) {
		config := &ErrorConfig{
			IncludeStackTrace: true,
			Environment:       "development",
			MaxErrorLength:    1000,
			SanitizeErrors:    false,
		}

		middleware := NewErrorMiddleware(logger, config)
		assert.NotNil(t, middleware)
		assert.Equal(t, config, middleware.config)
		assert.Equal(t, logger, middleware.logger)
	})

	t.Run("With Nil Config", func(t *testing.T) {
		middleware := NewErrorMiddleware(logger, nil)
		assert.NotNil(t, middleware)
		assert.NotNil(t, middleware.config)
		assert.False(t, middleware.config.IncludeStackTrace)
		assert.Equal(t, "production", middleware.config.Environment)
	})
}

// TestErrorHandling tests error handling functionality
// Task 1.4: Test error processing and response generation
func TestErrorHandling(t *testing.T) {
	router := setupTestRouter()

	t.Run("AuthError Handling", func(t *testing.T) {
		router.GET("/auth-error", func(c *gin.Context) {
			authErr := autherrors.ErrTokenExpired()
			c.Error(authErr)
		})

		req, _ := http.NewRequest("GET", "/auth-error", nil)
		req.Header.Set("X-Request-ID", "test-123")
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "TOKEN_EXPIRED")
		assert.Contains(t, w.Body.String(), "test-123")
	})

	t.Run("Regular Error Conversion", func(t *testing.T) {
		router.GET("/regular-error", func(c *gin.Context) {
			c.Error(errors.New("database connection failed"))
		})

		req, _ := http.NewRequest("GET", "/regular-error", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusServiceUnavailable, w.Code)
		assert.Contains(t, w.Body.String(), "DATABASE_ERROR")
	})
}

// TestPanicRecovery tests panic recovery functionality
// Task 1.4: Test panic recovery with proper error responses
func TestPanicRecovery(t *testing.T) {
	router := setupTestRouter()

	router.GET("/panic", func(c *gin.Context) {
		panic("test panic")
	})

	req, _ := http.NewRequest("GET", "/panic", nil)
	req.Header.Set("X-Request-ID", "panic-test")
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Contains(t, w.Body.String(), "INTERNAL_ERROR")
	assert.Contains(t, w.Body.String(), "panic-test")
}

// TestErrorConversion tests error type conversion
// Task 1.4: Test smart error categorization
func TestErrorConversion(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	config := &ErrorConfig{
		SanitizeErrors: true,
		MaxErrorLength: 500,
	}
	middleware := NewErrorMiddleware(logger, config)

	tests := []struct {
		name           string
		inputError     error
		expectedCode   autherrors.AuthErrorCode
		expectedStatus int
	}{
		{
			name:           "Token Expired Error",
			inputError:     errors.New("token has expired"),
			expectedCode:   autherrors.ErrCodeTokenExpired,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "Token Invalid Error",
			inputError:     errors.New("invalid token signature"),
			expectedCode:   autherrors.ErrCodeTokenInvalid,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "Token Missing Error",
			inputError:     errors.New("missing authorization token"),
			expectedCode:   autherrors.ErrCodeTokenMissing,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "User Not Found Error",
			inputError:     errors.New("user not found in database"),
			expectedCode:   autherrors.ErrCodeUserNotFound,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "Access Denied Error",
			inputError:     errors.New("access denied to resource"),
			expectedCode:   autherrors.ErrCodeAccessDenied,
			expectedStatus: http.StatusForbidden,
		},
		{
			name:           "Rate Limit Error",
			inputError:     errors.New("rate limit exceeded"),
			expectedCode:   autherrors.ErrCodeRateLimitExceeded,
			expectedStatus: http.StatusTooManyRequests,
		},
		{
			name:           "Mock Data Error",
			inputError:     errors.New("mock data detected in request"),
			expectedCode:   autherrors.ErrCodeMockDataDetected,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "Database Error",
			inputError:     errors.New("database connection timeout"),
			expectedCode:   autherrors.ErrCodeDatabaseError,
			expectedStatus: http.StatusServiceUnavailable,
		},
		{
			name:           "Network Error",
			inputError:     errors.New("network timeout occurred"),
			expectedCode:   autherrors.ErrCodeNetworkError,
			expectedStatus: http.StatusServiceUnavailable,
		},
		{
			name:           "Generic Error",
			inputError:     errors.New("something went wrong"),
			expectedCode:   autherrors.ErrCodeInternalError,
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			authErr := middleware.convertToAuthError(tt.inputError)
			assert.Equal(t, tt.expectedCode, authErr.Code)
			assert.Equal(t, tt.expectedStatus, authErr.HTTPStatus)
		})
	}
}

// TestErrorSanitization tests error message sanitization
// Task 1.4: Test sensitive information removal
func TestErrorSanitization(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	config := &ErrorConfig{
		SanitizeErrors: true,
		MaxErrorLength: 50,
	}
	middleware := NewErrorMiddleware(logger, config)

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Password in Error",
			input:    "authentication failed with password 123456",
			expected: "Sensitive information removed from error message",
		},
		{
			name:     "Secret in Error",
			input:    "invalid secret key provided",
			expected: "Sensitive information removed from error message",
		},
		{
			name:     "Long Error Message",
			input:    "this is a very long error message that exceeds the maximum length limit",
			expected: "this is a very long error message that exceeds the...",
		},
		{
			name:     "Normal Error",
			input:    "user not found",
			expected: "user not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := middleware.sanitizeError(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestValidationErrorHandler tests validation error handling
// Task 1.4: Test validation error processing
func TestValidationErrorHandler(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	config := &ErrorConfig{}
	middleware := NewErrorMiddleware(logger, config)

	router := gin.New()
	router.Use(middleware.ValidationErrorHandler())

	router.POST("/validate", func(c *gin.Context) {
		// Simulate a binding error
		c.Error(&gin.Error{
			Err:  errors.New("validation failed: email is required"),
			Type: gin.ErrorTypeBind,
		})
	})

	req, _ := http.NewRequest("POST", "/validate", nil)
	req.Header.Set("X-Request-ID", "validation-test")
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)
	assert.Contains(t, w.Body.String(), "VALIDATION_FAILED")
	assert.Contains(t, w.Body.String(), "validation-test")
}

// TestNotFoundHandler tests 404 error handling
// Task 1.4: Test 404 error responses
func TestNotFoundHandler(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	config := &ErrorConfig{}
	middleware := NewErrorMiddleware(logger, config)

	router := gin.New()
	router.NoRoute(middleware.NotFoundHandler())

	req, _ := http.NewRequest("GET", "/nonexistent", nil)
	req.Header.Set("X-Request-ID", "notfound-test")
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNotFound, w.Code)
	assert.Contains(t, w.Body.String(), "USER_NOT_FOUND")
	assert.Contains(t, w.Body.String(), "notfound-test")
	assert.Contains(t, w.Body.String(), "/nonexistent")
}

// TestMethodNotAllowedHandler tests 405 error handling
// Task 1.4: Test 405 error responses
func TestMethodNotAllowedHandler(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	config := &ErrorConfig{}
	middleware := NewErrorMiddleware(logger, config)

	router := gin.New()
	router.HandleMethodNotAllowed = true
	router.NoMethod(middleware.MethodNotAllowedHandler())
	router.GET("/test", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "success"})
	})

	req, _ := http.NewRequest("POST", "/test", nil)
	req.Header.Set("X-Request-ID", "method-test")
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusMethodNotAllowed, w.Code)
	assert.Contains(t, w.Body.String(), "VALIDATION_FAILED")
	assert.Contains(t, w.Body.String(), "method-test")
	assert.Contains(t, w.Body.String(), "POST")
}
