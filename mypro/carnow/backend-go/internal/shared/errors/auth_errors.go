package errors

import (
	"fmt"
	"net/http"
	"time"
)

// AuthErrorCode represents specific authentication error codes
// Task 1.4: Standardized error codes for different authentication failure scenarios
type AuthErrorCode string

const (
	// Token-related errors
	ErrCodeTokenMissing     AuthErrorCode = "TOKEN_MISSING"
	ErrCodeTokenInvalid     AuthErrorCode = "TOKEN_INVALID"
	ErrCodeTokenExpired     AuthErrorCode = "TOKEN_EXPIRED"
	ErrCodeTokenMalformed   AuthErrorCode = "TOKEN_MALFORMED"
	ErrCodeTokenRevoked     AuthErrorCode = "TOKEN_REVOKED"
	ErrCodeTokenBlacklisted AuthErrorCode = "TOKEN_BLACKLISTED"

	// Authentication errors
	ErrCodeAuthFailed        AuthErrorCode = "AUTH_FAILED"
	ErrCodeInvalidCredentials AuthErrorCode = "INVALID_CREDENTIALS"
	ErrCodeUserNotFound      AuthErrorCode = "USER_NOT_FOUND"
	ErrCodeUserDisabled      AuthErrorCode = "USER_DISABLED"
	ErrCodeAccountLocked     AuthErrorCode = "ACCOUNT_LOCKED"

	// Authorization errors
	ErrCodeInsufficientPermissions AuthErrorCode = "INSUFFICIENT_PERMISSIONS"
	ErrCodeAccessDenied            AuthErrorCode = "ACCESS_DENIED"
	ErrCodeRoleRequired            AuthErrorCode = "ROLE_REQUIRED"

	// Rate limiting errors
	ErrCodeRateLimitExceeded AuthErrorCode = "RATE_LIMIT_EXCEEDED"
	ErrCodeTooManyAttempts   AuthErrorCode = "TOO_MANY_ATTEMPTS"

	// Configuration errors
	ErrCodeConfigurationError AuthErrorCode = "CONFIGURATION_ERROR"
	ErrCodeServiceUnavailable AuthErrorCode = "SERVICE_UNAVAILABLE"

	// Validation errors
	ErrCodeValidationFailed AuthErrorCode = "VALIDATION_FAILED"
	ErrCodeMockDataDetected AuthErrorCode = "MOCK_DATA_DETECTED"
	ErrCodeRealDataRequired AuthErrorCode = "REAL_DATA_REQUIRED"

	// Internal errors
	ErrCodeInternalError AuthErrorCode = "INTERNAL_ERROR"
	ErrCodeDatabaseError AuthErrorCode = "DATABASE_ERROR"
	ErrCodeNetworkError  AuthErrorCode = "NETWORK_ERROR"
)

// AuthError represents a standardized authentication error
// Task 1.4: Comprehensive error structure with proper HTTP status codes
type AuthError struct {
	Code       AuthErrorCode `json:"code"`
	Message    string        `json:"message"`
	Details    string        `json:"details,omitempty"`
	HTTPStatus int           `json:"-"`
	Timestamp  time.Time     `json:"timestamp"`
	RequestID  string        `json:"request_id,omitempty"`
	UserID     string        `json:"user_id,omitempty"`
	ClientIP   string        `json:"client_ip,omitempty"`
	UserAgent  string        `json:"user_agent,omitempty"`
	Retryable  bool          `json:"retryable"`
}

// Error implements the error interface
func (e *AuthError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// NewAuthError creates a new authentication error
// Task 1.4: Factory function for creating standardized errors
func NewAuthError(code AuthErrorCode, message string, details ...string) *AuthError {
	err := &AuthError{
		Code:       code,
		Message:    message,
		HTTPStatus: getHTTPStatusForCode(code),
		Timestamp:  time.Now(),
		Retryable:  isRetryableError(code),
	}

	if len(details) > 0 {
		err.Details = details[0]
	}

	return err
}

// WithContext adds context information to the error
func (e *AuthError) WithContext(requestID, userID, clientIP, userAgent string) *AuthError {
	e.RequestID = requestID
	e.UserID = userID
	e.ClientIP = clientIP
	e.UserAgent = userAgent
	return e
}

// WithDetails adds additional details to the error
func (e *AuthError) WithDetails(details string) *AuthError {
	e.Details = details
	return e
}

// IsRetryable returns whether the error is retryable
func (e *AuthError) IsRetryable() bool {
	return e.Retryable
}

// ToHTTPResponse converts the error to an HTTP response format
// Task 1.4: Standardized HTTP error response format
func (e *AuthError) ToHTTPResponse() map[string]interface{} {
	response := map[string]interface{}{
		"error": map[string]interface{}{
			"code":      e.Code,
			"message":   e.Message,
			"timestamp": e.Timestamp.Format(time.RFC3339),
			"retryable": e.Retryable,
		},
		"success": false,
	}

	if e.Details != "" {
		response["error"].(map[string]interface{})["details"] = e.Details
	}

	if e.RequestID != "" {
		response["request_id"] = e.RequestID
	}

	return response
}

// getHTTPStatusForCode maps error codes to HTTP status codes
// Task 1.4: Proper HTTP status code mapping
func getHTTPStatusForCode(code AuthErrorCode) int {
	switch code {
	case ErrCodeTokenMissing, ErrCodeTokenInvalid, ErrCodeTokenExpired,
		 ErrCodeTokenMalformed, ErrCodeTokenRevoked, ErrCodeTokenBlacklisted,
		 ErrCodeAuthFailed, ErrCodeInvalidCredentials:
		return http.StatusUnauthorized // 401

	case ErrCodeUserNotFound:
		return http.StatusNotFound // 404

	case ErrCodeInsufficientPermissions, ErrCodeAccessDenied, ErrCodeRoleRequired,
		 ErrCodeUserDisabled, ErrCodeAccountLocked:
		return http.StatusForbidden // 403

	case ErrCodeRateLimitExceeded, ErrCodeTooManyAttempts:
		return http.StatusTooManyRequests // 429

	case ErrCodeValidationFailed, ErrCodeMockDataDetected, ErrCodeRealDataRequired:
		return http.StatusBadRequest // 400

	case ErrCodeServiceUnavailable, ErrCodeDatabaseError, ErrCodeNetworkError:
		return http.StatusServiceUnavailable // 503

	case ErrCodeConfigurationError:
		return http.StatusInternalServerError // 500

	case ErrCodeInternalError:
		return http.StatusInternalServerError // 500

	default:
		return http.StatusInternalServerError // 500
	}
}

// isRetryableError determines if an error is retryable
func isRetryableError(code AuthErrorCode) bool {
	switch code {
	case ErrCodeServiceUnavailable, ErrCodeDatabaseError, ErrCodeNetworkError,
		 ErrCodeInternalError:
		return true
	case ErrCodeRateLimitExceeded, ErrCodeTooManyAttempts:
		return true // Can retry after waiting
	default:
		return false
	}
}

// Common error constructors for frequently used errors
// Task 1.4: Convenience functions for common authentication errors

// ErrTokenMissing creates a token missing error
func ErrTokenMissing() *AuthError {
	return NewAuthError(ErrCodeTokenMissing, "Authentication token is required")
}

// ErrTokenInvalid creates a token invalid error
func ErrTokenInvalid(details string) *AuthError {
	return NewAuthError(ErrCodeTokenInvalid, "Authentication token is invalid", details)
}

// ErrTokenExpired creates a token expired error
func ErrTokenExpired() *AuthError {
	return NewAuthError(ErrCodeTokenExpired, "Authentication token has expired")
}

// ErrInvalidCredentials creates an invalid credentials error
func ErrInvalidCredentials() *AuthError {
	return NewAuthError(ErrCodeInvalidCredentials, "Invalid email or password")
}

// ErrUserNotFound creates a user not found error
func ErrUserNotFound() *AuthError {
	return NewAuthError(ErrCodeUserNotFound, "User not found")
}

// ErrAccessDenied creates an access denied error
func ErrAccessDenied(resource string) *AuthError {
	return NewAuthError(ErrCodeAccessDenied, "Access denied", fmt.Sprintf("Insufficient permissions to access %s", resource))
}

// ErrRateLimitExceeded creates a rate limit exceeded error
func ErrRateLimitExceeded(retryAfter time.Duration) *AuthError {
	return NewAuthError(ErrCodeRateLimitExceeded, "Rate limit exceeded", 
		fmt.Sprintf("Too many requests. Try again in %v", retryAfter))
}

// ErrMockDataDetected creates a mock data detected error (Forever Plan compliance)
func ErrMockDataDetected(field string) *AuthError {
	return NewAuthError(ErrCodeMockDataDetected, "Mock data detected", 
		fmt.Sprintf("Mock data found in field: %s. Real data is required", field))
}

// ErrServiceUnavailable creates a service unavailable error
func ErrServiceUnavailable(service string) *AuthError {
	return NewAuthError(ErrCodeServiceUnavailable, "Service temporarily unavailable", 
		fmt.Sprintf("The %s service is currently unavailable", service))
}

// ErrInternalError creates an internal error
func ErrInternalError(details string) *AuthError {
	return NewAuthError(ErrCodeInternalError, "Internal server error", details)
}

// ErrorResponse represents a standardized error response format
// Task 1.4: Consistent error response structure
type ErrorResponse struct {
	Success   bool                   `json:"success"`
	Error     *AuthError            `json:"error"`
	RequestID string                `json:"request_id,omitempty"`
	Timestamp time.Time             `json:"timestamp"`
	Meta      map[string]interface{} `json:"meta,omitempty"`
}

// NewErrorResponse creates a new error response
func NewErrorResponse(err *AuthError, requestID string) *ErrorResponse {
	return &ErrorResponse{
		Success:   false,
		Error:     err,
		RequestID: requestID,
		Timestamp: time.Now(),
	}
}

// WithMeta adds metadata to the error response
func (r *ErrorResponse) WithMeta(key string, value interface{}) *ErrorResponse {
	if r.Meta == nil {
		r.Meta = make(map[string]interface{})
	}
	r.Meta[key] = value
	return r
}
