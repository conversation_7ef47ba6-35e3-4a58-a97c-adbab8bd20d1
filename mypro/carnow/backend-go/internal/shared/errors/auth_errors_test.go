package errors

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestAuthErrorCreation tests creating new auth errors
// Task 1.4: Test standardized error creation
func TestAuthErrorCreation(t *testing.T) {
	tests := []struct {
		name              string
		code              AuthErrorCode
		message           string
		details           string
		expectedStatus    int
		expectedRetryable bool
	}{
		{
			name:              "Token Missing Error",
			code:              ErrCodeTokenMissing,
			message:           "Token is required",
			details:           "Authorization header missing",
			expectedStatus:    http.StatusUnauthorized,
			expectedRetryable: false,
		},
		{
			name:              "Token Expired Error",
			code:              ErrCodeTokenExpired,
			message:           "Token has expired",
			details:           "Token expired at 2024-01-01",
			expectedStatus:    http.StatusUnauthorized,
			expectedRetryable: false,
		},
		{
			name:              "Service Unavailable Error",
			code:              ErrCodeServiceUnavailable,
			message:           "Service temporarily unavailable",
			details:           "Database connection failed",
			expectedStatus:    http.StatusServiceUnavailable,
			expectedRetryable: true,
		},
		{
			name:              "Rate Limit Exceeded Error",
			code:              ErrCodeRateLimitExceeded,
			message:           "Too many requests",
			details:           "Rate limit: 100 requests per minute",
			expectedStatus:    http.StatusTooManyRequests,
			expectedRetryable: true,
		},
		{
			name:              "Mock Data Detected Error",
			code:              ErrCodeMockDataDetected,
			message:           "Mock data not allowed",
			details:           "Found mock data in user field",
			expectedStatus:    http.StatusBadRequest,
			expectedRetryable: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := NewAuthError(tt.code, tt.message, tt.details)

			assert.Equal(t, tt.code, err.Code)
			assert.Equal(t, tt.message, err.Message)
			assert.Equal(t, tt.details, err.Details)
			assert.Equal(t, tt.expectedStatus, err.HTTPStatus)
			assert.Equal(t, tt.expectedRetryable, err.Retryable)
			assert.NotZero(t, err.Timestamp)
		})
	}
}

// TestAuthErrorWithContext tests adding context to errors
// Task 1.4: Test error context functionality
func TestAuthErrorWithContext(t *testing.T) {
	err := NewAuthError(ErrCodeTokenInvalid, "Invalid token")

	requestID := "req-123"
	userID := "user-456"
	clientIP := "***********"
	userAgent := "Mozilla/5.0"

	err.WithContext(requestID, userID, clientIP, userAgent)

	assert.Equal(t, requestID, err.RequestID)
	assert.Equal(t, userID, err.UserID)
	assert.Equal(t, clientIP, err.ClientIP)
	assert.Equal(t, userAgent, err.UserAgent)
}

// TestAuthErrorHTTPResponse tests HTTP response generation
// Task 1.4: Test standardized HTTP response format
func TestAuthErrorHTTPResponse(t *testing.T) {
	err := NewAuthError(ErrCodeTokenExpired, "Token has expired", "Expired at 2024-01-01")
	err.WithContext("req-123", "user-456", "***********", "Mozilla/5.0")

	response := err.ToHTTPResponse()

	assert.False(t, response["success"].(bool))
	assert.Equal(t, "req-123", response["request_id"])

	errorData := response["error"].(map[string]interface{})
	assert.Equal(t, ErrCodeTokenExpired, errorData["code"])
	assert.Equal(t, "Token has expired", errorData["message"])
	assert.Equal(t, "Expired at 2024-01-01", errorData["details"])
	assert.False(t, errorData["retryable"].(bool))
	assert.NotEmpty(t, errorData["timestamp"])
}

// TestCommonErrorConstructors tests convenience error constructors
// Task 1.4: Test common error creation functions
func TestCommonErrorConstructors(t *testing.T) {
	tests := []struct {
		name           string
		errorFunc      func() *AuthError
		expectedCode   AuthErrorCode
		expectedStatus int
	}{
		{
			name:           "Token Missing",
			errorFunc:      func() *AuthError { return ErrTokenMissing() },
			expectedCode:   ErrCodeTokenMissing,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "Token Expired",
			errorFunc:      func() *AuthError { return ErrTokenExpired() },
			expectedCode:   ErrCodeTokenExpired,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "Invalid Credentials",
			errorFunc:      func() *AuthError { return ErrInvalidCredentials() },
			expectedCode:   ErrCodeInvalidCredentials,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "User Not Found",
			errorFunc:      func() *AuthError { return ErrUserNotFound() },
			expectedCode:   ErrCodeUserNotFound,
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.errorFunc()
			assert.Equal(t, tt.expectedCode, err.Code)
			assert.Equal(t, tt.expectedStatus, err.HTTPStatus)
			assert.NotEmpty(t, err.Message)
		})
	}
}

// TestErrorWithDetails tests adding details to errors
// Task 1.4: Test error details functionality
func TestErrorWithDetails(t *testing.T) {
	err := NewAuthError(ErrCodeTokenInvalid, "Invalid token")
	details := "Token signature verification failed"

	err.WithDetails(details)

	assert.Equal(t, details, err.Details)
}

// TestErrorInterface tests that AuthError implements error interface
// Task 1.4: Test error interface implementation
func TestErrorInterface(t *testing.T) {
	err := NewAuthError(ErrCodeTokenExpired, "Token has expired", "Expired at 2024-01-01")

	// Test that it implements error interface
	var _ error = err

	// Test Error() method
	errorString := err.Error()
	assert.Contains(t, errorString, "TOKEN_EXPIRED")
	assert.Contains(t, errorString, "Token has expired")
	assert.Contains(t, errorString, "Expired at 2024-01-01")
}

// TestErrorWithoutDetails tests error string without details
// Task 1.4: Test error string formatting
func TestErrorWithoutDetails(t *testing.T) {
	err := NewAuthError(ErrCodeTokenMissing, "Token is required")

	errorString := err.Error()
	assert.Equal(t, "TOKEN_MISSING: Token is required", errorString)
}

// TestRetryableErrors tests retryable error detection
// Task 1.4: Test retryable error logic
func TestRetryableErrors(t *testing.T) {
	retryableTests := []struct {
		code      AuthErrorCode
		retryable bool
	}{
		{ErrCodeServiceUnavailable, true},
		{ErrCodeDatabaseError, true},
		{ErrCodeNetworkError, true},
		{ErrCodeInternalError, true},
		{ErrCodeRateLimitExceeded, true},
		{ErrCodeTooManyAttempts, true},
		{ErrCodeTokenExpired, false},
		{ErrCodeTokenInvalid, false},
		{ErrCodeUserNotFound, false},
		{ErrCodeAccessDenied, false},
	}

	for _, tt := range retryableTests {
		t.Run(string(tt.code), func(t *testing.T) {
			err := NewAuthError(tt.code, "Test message")
			assert.Equal(t, tt.retryable, err.IsRetryable())
		})
	}
}

// TestHTTPStatusMapping tests HTTP status code mapping
// Task 1.4: Test proper HTTP status code assignment
func TestHTTPStatusMapping(t *testing.T) {
	statusTests := []struct {
		code           AuthErrorCode
		expectedStatus int
	}{
		{ErrCodeTokenMissing, http.StatusUnauthorized},
		{ErrCodeTokenInvalid, http.StatusUnauthorized},
		{ErrCodeTokenExpired, http.StatusUnauthorized},
		{ErrCodeUserNotFound, http.StatusNotFound},
		{ErrCodeAccessDenied, http.StatusForbidden},
		{ErrCodeRateLimitExceeded, http.StatusTooManyRequests},
		{ErrCodeValidationFailed, http.StatusBadRequest},
		{ErrCodeServiceUnavailable, http.StatusServiceUnavailable},
		{ErrCodeInternalError, http.StatusInternalServerError},
	}

	for _, tt := range statusTests {
		t.Run(string(tt.code), func(t *testing.T) {
			err := NewAuthError(tt.code, "Test message")
			assert.Equal(t, tt.expectedStatus, err.HTTPStatus)
		})
	}
}

// TestSpecialErrorConstructors tests special error constructors
// Task 1.4: Test specialized error creation functions
func TestSpecialErrorConstructors(t *testing.T) {
	t.Run("Access Denied with Resource", func(t *testing.T) {
		err := ErrAccessDenied("admin panel")
		assert.Equal(t, ErrCodeAccessDenied, err.Code)
		assert.Contains(t, err.Details, "admin panel")
	})

	t.Run("Rate Limit with Retry After", func(t *testing.T) {
		retryAfter := 5 * time.Minute
		err := ErrRateLimitExceeded(retryAfter)
		assert.Equal(t, ErrCodeRateLimitExceeded, err.Code)
		assert.Contains(t, err.Details, "5m0s")
	})

	t.Run("Mock Data Detected with Field", func(t *testing.T) {
		err := ErrMockDataDetected("user_email")
		assert.Equal(t, ErrCodeMockDataDetected, err.Code)
		assert.Contains(t, err.Details, "user_email")
	})

	t.Run("Service Unavailable with Service Name", func(t *testing.T) {
		err := ErrServiceUnavailable("database")
		assert.Equal(t, ErrCodeServiceUnavailable, err.Code)
		assert.Contains(t, err.Details, "database")
	})

	t.Run("Internal Error with Details", func(t *testing.T) {
		details := "Database connection pool exhausted"
		err := ErrInternalError(details)
		assert.Equal(t, ErrCodeInternalError, err.Code)
		assert.Equal(t, details, err.Details)
	})
}

// TestErrorResponse tests error response structure
// Task 1.4: Test error response wrapper
func TestErrorResponse(t *testing.T) {
	authErr := NewAuthError(ErrCodeTokenExpired, "Token has expired")
	requestID := "req-123"

	response := NewErrorResponse(authErr, requestID)

	assert.False(t, response.Success)
	assert.Equal(t, authErr, response.Error)
	assert.Equal(t, requestID, response.RequestID)
	assert.NotZero(t, response.Timestamp)

	// Test with meta
	response.WithMeta("retry_after", 300)
	assert.Equal(t, 300, response.Meta["retry_after"])
}
