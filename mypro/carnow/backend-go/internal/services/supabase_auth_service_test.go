package services

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/shared/errors"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
)

// TestSupabaseAuthService_NewSupabaseAuthService tests service initialization
// Task 2.3: Test service creation with various configurations
func TestSupabaseAuthService_NewSupabaseAuthService(t *testing.T) {
	logger, _ := zap.NewDevelopment()

	tests := []struct {
		name        string
		config      *config.Config
		logger      *zap.Logger
		expectError bool
		errorType   string
	}{
		{
			name: "Valid Configuration",
			config: &config.Config{
				Supabase: config.SupabaseConfig{
					URL:     "https://lpxtghyvxuenyyisrrro.supabase.co",
					Anon<PERSON>ey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE2NDA5OTUyMDAsImV4cCI6MTk1NjM3MTIwMH0",
				},
			},
			logger:      logger,
			expectError: false,
		},
		{
			name:        "Nil Configuration",
			config:      nil,
			logger:      logger,
			expectError: true,
			errorType:   "INTERNAL_ERROR",
		},
		{
			name: "Missing URL",
			config: &config.Config{
				Supabase: config.SupabaseConfig{
					AnonKey: "valid-anon-key-here",
				},
			},
			logger:      logger,
			expectError: true,
			errorType:   "CONFIGURATION_ERROR",
		},
		{
			name: "Missing Anon Key",
			config: &config.Config{
				Supabase: config.SupabaseConfig{
					URL: "https://lpxtghyvxuenyyisrrro.supabase.co",
				},
			},
			logger:      logger,
			expectError: true,
			errorType:   "CONFIGURATION_ERROR",
		},
		{
			name: "Mock Data in URL",
			config: &config.Config{
				Supabase: config.SupabaseConfig{
					URL:     "https://mock-project.supabase.co",
					AnonKey: "valid-anon-key-here",
				},
			},
			logger:      logger,
			expectError: true,
			errorType:   "MOCK_DATA_DETECTED",
		},
		{
			name:        "Nil Logger",
			config:      &config.Config{},
			logger:      nil,
			expectError: true,
			errorType:   "INTERNAL_ERROR",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := NewSupabaseAuthService(tt.config, tt.logger)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, service)

				if tt.errorType != "" {
					authErr, ok := err.(*errors.AuthError)
					require.True(t, ok, "Expected AuthError")
					assert.Contains(t, string(authErr.Code), tt.errorType)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, service)
				if tt.config != nil && service != nil {
					assert.Equal(t, strings.TrimSuffix(tt.config.Supabase.URL, "/"), service.baseURL)
					assert.Equal(t, tt.config.Supabase.AnonKey, service.apiKey)
				}
			}
		})
	}
}

// TestSupabaseAuthService_SignInWithEmail tests email/password authentication
// Task 2.3: Test sign in with various scenarios
func TestSupabaseAuthService_SignInWithEmail(t *testing.T) {
	// Create mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "POST", r.Method)
		assert.Equal(t, "/auth/v1/token", r.URL.Path)
		assert.Equal(t, "grant_type=password", r.URL.RawQuery)
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.NotEmpty(t, r.Header.Get("apikey"))

		// Parse request body
		var authReq AuthRequest
		err := json.NewDecoder(r.Body).Decode(&authReq)
		require.NoError(t, err)

		// Mock responses based on email
		switch authReq.Email {
		case "<EMAIL>":
			// Success response
			response := AuthResponse{
				AccessToken:  "valid-access-token",
				RefreshToken: "valid-refresh-token",
				ExpiresIn:    3600,
				TokenType:    "Bearer",
				User: &User{
					ID:             "550e8400-e29b-41d4-a716-446655440000",
					Email:          "<EMAIL>",
					EmailConfirmed: true,
					CreatedAt:      time.Now(),
					UpdatedAt:      time.Now(),
					Role:           "authenticated",
				},
			}
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(response)

		case "<EMAIL>":
			// Invalid credentials
			w.WriteHeader(http.StatusBadRequest)
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]string{
				"error":             "invalid_grant",
				"error_description": "Invalid login credentials",
			})

		case "<EMAIL>":
			// Email not confirmed
			w.WriteHeader(http.StatusBadRequest)
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]string{
				"error":             "email_not_confirmed",
				"error_description": "Email not confirmed",
			})

		default:
			w.WriteHeader(http.StatusInternalServerError)
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]string{
				"error":   "server_error",
				"message": "Internal server error",
			})
		}
	}))
	defer server.Close()

	// Create service with mock server
	logger, _ := zap.NewDevelopment()
	cfg := &config.Config{
		Supabase: config.SupabaseConfig{
			URL:     server.URL,
			AnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb2plY3QiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MDk5NTIwMCwiZXhwIjoxOTU2MzcxMjAwfQ",
		},
	}
	service, err := NewSupabaseAuthService(cfg, logger)
	require.NoError(t, err)

	tests := []struct {
		name        string
		email       string
		password    string
		expectError bool
		errorType   string
	}{
		{
			name:        "Valid Credentials",
			email:       "<EMAIL>",
			password:    "validpassword",
			expectError: false,
		},
		{
			name:        "Invalid Credentials",
			email:       "<EMAIL>",
			password:    "wrongpassword",
			expectError: true,
			errorType:   "INVALID_CREDENTIALS",
		},
		{
			name:        "Email Not Confirmed",
			email:       "<EMAIL>",
			password:    "validpassword",
			expectError: true,
			errorType:   "USER_DISABLED",
		},
		{
			name:        "Empty Email",
			email:       "",
			password:    "validpassword",
			expectError: true,
			errorType:   "VALIDATION_FAILED",
		},
		{
			name:        "Empty Password",
			email:       "<EMAIL>",
			password:    "",
			expectError: true,
			errorType:   "VALIDATION_FAILED",
		},
		{
			name:        "Mock Email",
			email:       "<EMAIL>",
			password:    "validpassword",
			expectError: true,
			errorType:   "MOCK_DATA_DETECTED",
		},
		{
			name:        "Mock Password",
			email:       "<EMAIL>",
			password:    "mock_password",
			expectError: true,
			errorType:   "MOCK_DATA_DETECTED",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := service.SignInWithEmail(ctx, tt.email, tt.password)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)

				if tt.errorType != "" {
					authErr, ok := err.(*errors.AuthError)
					require.True(t, ok, "Expected AuthError")
					assert.Contains(t, string(authErr.Code), tt.errorType)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "valid-access-token", result.AccessToken)
				assert.Equal(t, "valid-refresh-token", result.RefreshToken)
				assert.Equal(t, 3600, result.ExpiresIn)
				assert.Equal(t, "Bearer", result.TokenType)
				assert.NotNil(t, result.User)
				assert.Equal(t, "<EMAIL>", result.User.Email)
				assert.True(t, result.ExpiresAt.After(time.Now()))
			}
		})
	}
}

// TestSupabaseAuthService_SignOut tests user sign out
// Task 2.3: Test sign out with various scenarios
func TestSupabaseAuthService_SignOut(t *testing.T) {
	// Create mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "POST", r.Method)
		assert.Equal(t, "/auth/v1/logout", r.URL.Path)
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.NotEmpty(t, r.Header.Get("apikey"))

		authHeader := r.Header.Get("Authorization")
		switch authHeader {
		case "Bearer valid-token":
			w.WriteHeader(http.StatusNoContent)
		case "Bearer expired-token":
			w.WriteHeader(http.StatusUnauthorized)
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]string{
				"error":             "invalid_token",
				"error_description": "Token has expired",
			})
		case "Bearer invalid-token":
			w.WriteHeader(http.StatusUnauthorized)
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]string{
				"error":             "invalid_token",
				"error_description": "Invalid token",
			})
		default:
			w.WriteHeader(http.StatusBadRequest)
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]string{
				"error":   "bad_request",
				"message": "Invalid request",
			})
		}
	}))
	defer server.Close()

	// Create service with mock server
	logger, _ := zap.NewDevelopment()
	cfg := &config.Config{
		Supabase: config.SupabaseConfig{
			URL:     server.URL,
			AnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb2plY3QiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MDk5NTIwMCwiZXhwIjoxOTU2MzcxMjAwfQ",
		},
	}
	service, err := NewSupabaseAuthService(cfg, logger)
	require.NoError(t, err)

	tests := []struct {
		name        string
		accessToken string
		expectError bool
		errorType   string
	}{
		{
			name:        "Valid Token",
			accessToken: "valid-token",
			expectError: false,
		},
		{
			name:        "Expired Token",
			accessToken: "expired-token",
			expectError: true,
			errorType:   "TOKEN_INVALID",
		},
		{
			name:        "Invalid Token",
			accessToken: "invalid-token",
			expectError: true,
			errorType:   "TOKEN_INVALID",
		},
		{
			name:        "Empty Token",
			accessToken: "",
			expectError: true,
			errorType:   "VALIDATION_FAILED",
		},
		{
			name:        "Mock Token",
			accessToken: "mock_token",
			expectError: true,
			errorType:   "MOCK_DATA_DETECTED",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			err := service.SignOut(ctx, tt.accessToken)

			if tt.expectError {
				assert.Error(t, err)

				if tt.errorType != "" {
					authErr, ok := err.(*errors.AuthError)
					require.True(t, ok, "Expected AuthError")
					assert.Contains(t, string(authErr.Code), tt.errorType)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestSupabaseAuthService_RefreshToken tests token refresh
// Task 2.3: Test token refresh with various scenarios
func TestSupabaseAuthService_RefreshToken(t *testing.T) {
	// Create mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "POST", r.Method)
		assert.Equal(t, "/auth/v1/token", r.URL.Path)
		assert.Equal(t, "grant_type=refresh_token", r.URL.RawQuery)
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.NotEmpty(t, r.Header.Get("apikey"))

		// Parse request body
		var refreshReq RefreshTokenRequest
		err := json.NewDecoder(r.Body).Decode(&refreshReq)
		require.NoError(t, err)

		// Mock responses based on refresh token
		switch refreshReq.RefreshToken {
		case "valid-refresh-token":
			// Success response
			response := AuthResponse{
				AccessToken:  "new-access-token",
				RefreshToken: "new-refresh-token",
				ExpiresIn:    3600,
				TokenType:    "Bearer",
				User: &User{
					ID:             "550e8400-e29b-41d4-a716-446655440000",
					Email:          "<EMAIL>",
					EmailConfirmed: true,
					CreatedAt:      time.Now(),
					UpdatedAt:      time.Now(),
					Role:           "authenticated",
				},
			}
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(response)

		case "expired-refresh-token":
			// Expired token
			w.WriteHeader(http.StatusUnauthorized)
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]string{
				"error":             "token_expired",
				"error_description": "Refresh token has expired",
			})

		case "invalid-refresh-token":
			// Invalid token
			w.WriteHeader(http.StatusUnauthorized)
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]string{
				"error":             "invalid_token",
				"error_description": "Invalid refresh token",
			})

		default:
			w.WriteHeader(http.StatusBadRequest)
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]string{
				"error":   "bad_request",
				"message": "Invalid request",
			})
		}
	}))
	defer server.Close()

	// Create service with mock server
	logger, _ := zap.NewDevelopment()
	cfg := &config.Config{
		Supabase: config.SupabaseConfig{
			URL:     server.URL,
			AnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb2plY3QiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MDk5NTIwMCwiZXhwIjoxOTU2MzcxMjAwfQ",
		},
	}
	service, err := NewSupabaseAuthService(cfg, logger)
	require.NoError(t, err)

	tests := []struct {
		name         string
		refreshToken string
		expectError  bool
		errorType    string
	}{
		{
			name:         "Valid Refresh Token",
			refreshToken: "valid-refresh-token",
			expectError:  false,
		},
		{
			name:         "Expired Refresh Token",
			refreshToken: "expired-refresh-token",
			expectError:  true,
			errorType:    "TOKEN_EXPIRED",
		},
		{
			name:         "Invalid Refresh Token",
			refreshToken: "invalid-refresh-token",
			expectError:  true,
			errorType:    "TOKEN_INVALID",
		},
		{
			name:         "Empty Refresh Token",
			refreshToken: "",
			expectError:  true,
			errorType:    "VALIDATION_FAILED",
		},
		{
			name:         "Mock Refresh Token",
			refreshToken: "mock_refresh_token",
			expectError:  true,
			errorType:    "MOCK_DATA_DETECTED",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := service.RefreshToken(ctx, tt.refreshToken)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)

				if tt.errorType != "" {
					authErr, ok := err.(*errors.AuthError)
					require.True(t, ok, "Expected AuthError")
					assert.Contains(t, string(authErr.Code), tt.errorType)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "new-access-token", result.AccessToken)
				assert.Equal(t, "new-refresh-token", result.RefreshToken)
				assert.Equal(t, 3600, result.ExpiresIn)
				assert.Equal(t, "Bearer", result.TokenType)
				assert.NotNil(t, result.User)
				assert.True(t, result.ExpiresAt.After(time.Now()))
			}
		})
	}
}

// TestSupabaseAuthService_GetUser tests user retrieval
// Task 2.3: Test get user with various scenarios
func TestSupabaseAuthService_GetUser(t *testing.T) {
	// Create mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "GET", r.Method)
		assert.Equal(t, "/auth/v1/user", r.URL.Path)
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.NotEmpty(t, r.Header.Get("apikey"))

		authHeader := r.Header.Get("Authorization")
		switch authHeader {
		case "Bearer valid-access-token":
			// Success response
			user := User{
				ID:             "550e8400-e29b-41d4-a716-446655440000",
				Email:          "<EMAIL>",
				EmailConfirmed: true,
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
				Role:           "authenticated",
				UserMetadata: map[string]any{
					"name": "Test User",
				},
			}
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(user)

		case "Bearer expired-access-token":
			// Expired token
			w.WriteHeader(http.StatusUnauthorized)
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]string{
				"error":             "token_expired",
				"error_description": "Access token has expired",
			})

		case "Bearer invalid-access-token":
			// Invalid token
			w.WriteHeader(http.StatusUnauthorized)
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]string{
				"error":             "invalid_token",
				"error_description": "Invalid access token",
			})

		default:
			w.WriteHeader(http.StatusBadRequest)
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]string{
				"error":   "bad_request",
				"message": "Invalid request",
			})
		}
	}))
	defer server.Close()

	// Create service with mock server
	logger, _ := zap.NewDevelopment()
	cfg := &config.Config{
		Supabase: config.SupabaseConfig{
			URL:     server.URL,
			AnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb2plY3QiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MDk5NTIwMCwiZXhwIjoxOTU2MzcxMjAwfQ",
		},
	}
	service, err := NewSupabaseAuthService(cfg, logger)
	require.NoError(t, err)

	tests := []struct {
		name        string
		accessToken string
		expectError bool
		errorType   string
	}{
		{
			name:        "Valid Access Token",
			accessToken: "valid-access-token",
			expectError: false,
		},
		{
			name:        "Expired Access Token",
			accessToken: "expired-access-token",
			expectError: true,
			errorType:   "TOKEN_EXPIRED",
		},
		{
			name:        "Invalid Access Token",
			accessToken: "invalid-access-token",
			expectError: true,
			errorType:   "TOKEN_INVALID",
		},
		{
			name:        "Empty Access Token",
			accessToken: "",
			expectError: true,
			errorType:   "VALIDATION_FAILED",
		},
		{
			name:        "Mock Access Token",
			accessToken: "mock_access_token",
			expectError: true,
			errorType:   "MOCK_DATA_DETECTED",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := service.GetUser(ctx, tt.accessToken)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)

				if tt.errorType != "" {
					authErr, ok := err.(*errors.AuthError)
					require.True(t, ok, "Expected AuthError")
					assert.Contains(t, string(authErr.Code), tt.errorType)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "550e8400-e29b-41d4-a716-446655440000", result.ID)
				assert.Equal(t, "<EMAIL>", result.Email)
				assert.True(t, result.EmailConfirmed)
				assert.Equal(t, "authenticated", result.Role)
				assert.NotNil(t, result.UserMetadata)
			}
		})
	}
}

// TestSupabaseAuthService_ContextTimeout tests context timeout handling
// Task 2.3: Test timeout scenarios
func TestSupabaseAuthService_ContextTimeout(t *testing.T) {
	// Create slow mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(2 * time.Second) // Simulate slow response
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	// Create service with mock server
	logger, _ := zap.NewDevelopment()
	cfg := &config.Config{
		Supabase: config.SupabaseConfig{
			URL:     server.URL,
			AnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb2plY3QiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MDk5NTIwMCwiZXhwIjoxOTU2MzcxMjAwfQ",
		},
	}
	service, err := NewSupabaseAuthService(cfg, logger)
	require.NoError(t, err)

	// Test with short timeout
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	_, err = service.SignInWithEmail(ctx, "<EMAIL>", "password123")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "context deadline exceeded")
}
