package services

import (
	"testing"
	"time"

	"carnow-backend/internal/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// createTestConfig creates a test configuration for JWT service
func createTestConfig() *config.Config {
	return &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-that-is-at-least-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-users",
		},
	}
}

// TestNewJWTService tests the JWT service initialization
func TestNewJWTService(t *testing.T) {
	tests := []struct {
		name        string
		config      *config.Config
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid Configuration",
			config:      createTestConfig(),
			expectError: false,
		},
		{
			name:        "Nil Configuration",
			config:      nil,
			expectError: true,
			errorMsg:    "configuration cannot be nil",
		},
		{
			name: "Empty JWT Secret",
			config: &config.Config{
				JWT: config.JWTConfig{
					Secret: "",
				},
			},
			expectError: true,
			errorMsg:    "JWT secret is required",
		},
		{
			name: "Short JWT Secret",
			config: &config.Config{
				JWT: config.JWTConfig{
					Secret: "short",
				},
			},
			expectError: true,
			errorMsg:    "JWT secret must be at least 32 characters long",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := NewJWTService(tt.config)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
				assert.Nil(t, service)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, service)
				assert.NotEmpty(t, service.keyID)
				assert.NotNil(t, service.revokedTokens)
			}
		})
	}
}

// TestValidateTokenInputs tests the input validation for token generation
func TestValidateTokenInputs(t *testing.T) {
	service, err := NewJWTService(createTestConfig())
	require.NoError(t, err)

	tests := []struct {
		name        string
		userID      string
		email       string
		fullName    string
		role        string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid Inputs",
			userID:      "user123",
			email:       "<EMAIL>",
			fullName:    "John Doe",
			role:        "user",
			expectError: false,
		},
		{
			name:        "Empty User ID",
			userID:      "",
			email:       "<EMAIL>",
			fullName:    "John Doe",
			role:        "user",
			expectError: true,
			errorMsg:    "user ID cannot be empty",
		},
		{
			name:        "Empty Email",
			userID:      "user123",
			email:       "",
			fullName:    "John Doe",
			role:        "user",
			expectError: true,
			errorMsg:    "email cannot be empty",
		},
		{
			name:        "Invalid Email Format",
			userID:      "user123",
			email:       "invalid-email",
			fullName:    "John Doe",
			role:        "user",
			expectError: true,
			errorMsg:    "invalid email format",
		},
		{
			name:        "Mock Data in User ID",
			userID:      "mock_user123",
			email:       "<EMAIL>",
			fullName:    "John Doe",
			role:        "user",
			expectError: true,
			errorMsg:    "mock data detected in user ID",
		},
		{
			name:        "Test Data in Email",
			userID:      "user123",
			email:       "<EMAIL>",
			fullName:    "John Doe",
			role:        "user",
			expectError: true,
			errorMsg:    "mock data detected in email",
		},
		{
			name:        "Fake Data in Full Name",
			userID:      "user123",
			email:       "<EMAIL>",
			fullName:    "fake_John Doe",
			role:        "user",
			expectError: true,
			errorMsg:    "mock data detected in full name",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.validateTokenInputs(tt.userID, tt.email, tt.fullName, tt.role)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestGenerateTokens tests token generation functionality
func TestGenerateTokens(t *testing.T) {
	service, err := NewJWTService(createTestConfig())
	require.NoError(t, err)

	tests := []struct {
		name        string
		userID      string
		email       string
		fullName    string
		role        string
		expectError bool
	}{
		{
			name:        "Valid Token Generation",
			userID:      "user123",
			email:       "<EMAIL>",
			fullName:    "John Doe",
			role:        "user",
			expectError: false,
		},
		{
			name:        "Invalid Input - Empty User ID",
			userID:      "",
			email:       "<EMAIL>",
			fullName:    "John Doe",
			role:        "user",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			accessToken, refreshToken, err := service.GenerateTokens(tt.userID, tt.email, tt.fullName, tt.role)

			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, accessToken)
				assert.Empty(t, refreshToken)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, accessToken)
				assert.NotEmpty(t, refreshToken)
			}
		})
	}
}

// TestGenerateTokensWithClientIP tests token generation with client IP
func TestGenerateTokensWithClientIP(t *testing.T) {
	service, err := NewJWTService(createTestConfig())
	require.NoError(t, err)

	userID := "user123"
	email := "<EMAIL>"
	fullName := "John Doe"
	role := "user"
	clientIP := "*************"

	accessToken, refreshToken, err := service.GenerateTokensWithClientIP(userID, email, fullName, role, clientIP)

	assert.NoError(t, err)
	assert.NotEmpty(t, accessToken)
	assert.NotEmpty(t, refreshToken)

	// Validate that the access token contains the client IP
	result := service.ValidateTokenWithDetails(accessToken)
	assert.True(t, result.Valid)
	assert.Equal(t, clientIP, result.Claims.ClientIP)
	assert.Equal(t, "access", result.Claims.TokenType)

	// Validate that the refresh token contains the client IP
	refreshResult := service.ValidateTokenWithDetails(refreshToken)
	assert.True(t, refreshResult.Valid)
	assert.Equal(t, clientIP, refreshResult.Claims.ClientIP)
	assert.Equal(t, "refresh", refreshResult.Claims.TokenType)
}

// TestValidateTokenWithDetails tests detailed token validation
func TestValidateTokenWithDetails(t *testing.T) {
	service, err := NewJWTService(createTestConfig())
	require.NoError(t, err)

	// Generate a valid token
	accessToken, _, err := service.GenerateTokens("user123", "<EMAIL>", "John Doe", "user")
	require.NoError(t, err)

	tests := []struct {
		name          string
		token         string
		expectedValid bool
		expectedCode  string
	}{
		{
			name:          "Valid Token",
			token:         accessToken,
			expectedValid: true,
		},
		{
			name:          "Empty Token",
			token:         "",
			expectedValid: false,
			expectedCode:  "TOKEN_EMPTY",
		},
		{
			name:          "Invalid Token Format",
			token:         "invalid.token.format",
			expectedValid: false,
			expectedCode:  "TOKEN_INVALID",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.ValidateTokenWithDetails(tt.token)

			assert.Equal(t, tt.expectedValid, result.Valid)
			if !tt.expectedValid {
				assert.Equal(t, tt.expectedCode, result.ErrorCode)
				assert.NotEmpty(t, result.Error)
			}
		})
	}
}

// TestTokenRevocation tests token revocation functionality
func TestTokenRevocation(t *testing.T) {
	service, err := NewJWTService(createTestConfig())
	require.NoError(t, err)

	// Generate a token
	accessToken, _, err := service.GenerateTokens("user123", "<EMAIL>", "John Doe", "user")
	require.NoError(t, err)

	// Validate token is initially valid
	result := service.ValidateTokenWithDetails(accessToken)
	assert.True(t, result.Valid)

	// Revoke the token
	err = service.RevokeToken(accessToken)
	assert.NoError(t, err)

	// Validate token is now revoked
	result = service.ValidateTokenWithDetails(accessToken)
	assert.False(t, result.Valid)
	assert.Equal(t, "TOKEN_REVOKED", result.ErrorCode)
}

// TestRefreshToken tests refresh token functionality
func TestRefreshToken(t *testing.T) {
	service, err := NewJWTService(createTestConfig())
	require.NoError(t, err)

	// Generate initial tokens
	_, refreshToken, err := service.GenerateTokens("user123", "<EMAIL>", "John Doe", "user")
	require.NoError(t, err)

	// Refresh the token
	tokenPair, err := service.RefreshToken(refreshToken)
	assert.NoError(t, err)
	assert.NotNil(t, tokenPair)
	assert.NotEmpty(t, tokenPair.AccessToken)
	assert.NotEmpty(t, tokenPair.RefreshToken)
	assert.Equal(t, "Bearer", tokenPair.TokenType)

	// Validate new access token
	result := service.ValidateTokenWithDetails(tokenPair.AccessToken)
	assert.True(t, result.Valid)
	assert.Equal(t, "user123", result.Claims.UserID)

	// Validate old refresh token is revoked
	oldResult := service.ValidateTokenWithDetails(refreshToken)
	assert.False(t, oldResult.Valid)
	assert.Equal(t, "TOKEN_REVOKED", oldResult.ErrorCode)
}
