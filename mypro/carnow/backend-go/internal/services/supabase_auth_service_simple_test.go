package services

import (
	"context"
	"strings"
	"testing"
	"time"

	"carnow-backend/internal/config"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

// TestSupabaseAuthService_BasicValidation tests basic service validation
// Task 2.3: Simple test for service validation
func TestSupabaseAuthService_BasicValidation(t *testing.T) {
	logger, _ := zap.NewDevelopment()

	t.Run("Valid Configuration", func(t *testing.T) {
		cfg := &config.Config{
			Supabase: config.SupabaseConfig{
				URL:     "https://project.supabase.co",
				Anon<PERSON><PERSON>: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb2plY3QiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MDk5NTIwMCwiZXhwIjoxOTU2MzcxMjAwfQ",
			},
		}
		service, err := NewSupabaseAuthService(cfg, logger)
		assert.NoError(t, err)
		assert.NotNil(t, service)
	})

	t.Run("Nil Configuration", func(t *testing.T) {
		service, err := NewSupabaseAuthService(nil, logger)
		assert.Error(t, err)
		assert.Nil(t, service)
	})

	t.Run("Missing URL", func(t *testing.T) {
		cfg := &config.Config{
			Supabase: config.SupabaseConfig{
				AnonKey: "valid-anon-key",
			},
		}
		service, err := NewSupabaseAuthService(cfg, logger)
		assert.Error(t, err)
		assert.Nil(t, service)
	})

	t.Run("Missing Anon Key", func(t *testing.T) {
		cfg := &config.Config{
			Supabase: config.SupabaseConfig{
				URL: "https://project.supabase.co",
			},
		}
		service, err := NewSupabaseAuthService(cfg, logger)
		assert.Error(t, err)
		assert.Nil(t, service)
	})
}

// TestSupabaseAuthService_InputValidation tests input validation
// Task 2.3: Test input validation for all methods
func TestSupabaseAuthService_InputValidation(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	cfg := &config.Config{
		Supabase: config.SupabaseConfig{
			URL:     "https://project.supabase.co",
			AnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb2plY3QiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MDk5NTIwMCwiZXhwIjoxOTU2MzcxMjAwfQ",
		},
	}
	service, err := NewSupabaseAuthService(cfg, logger)
	assert.NoError(t, err)
	assert.NotNil(t, service)

	ctx := context.Background()

	t.Run("SignInWithEmail - Empty Email", func(t *testing.T) {
		_, err := service.SignInWithEmail(ctx, "", "password")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "VALIDATION_FAILED")
	})

	t.Run("SignInWithEmail - Empty Password", func(t *testing.T) {
		_, err := service.SignInWithEmail(ctx, "<EMAIL>", "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "VALIDATION_FAILED")
	})

	t.Run("SignInWithEmail - Mock Email", func(t *testing.T) {
		_, err := service.SignInWithEmail(ctx, "<EMAIL>", "password")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "MOCK_DATA_DETECTED")
	})

	t.Run("SignOut - Empty Token", func(t *testing.T) {
		err := service.SignOut(ctx, "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "VALIDATION_FAILED")
	})

	t.Run("SignOut - Mock Token", func(t *testing.T) {
		err := service.SignOut(ctx, "mock_token")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "MOCK_DATA_DETECTED")
	})

	t.Run("RefreshToken - Empty Token", func(t *testing.T) {
		_, err := service.RefreshToken(ctx, "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "VALIDATION_FAILED")
	})

	t.Run("RefreshToken - Mock Token", func(t *testing.T) {
		_, err := service.RefreshToken(ctx, "mock_refresh_token")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "MOCK_DATA_DETECTED")
	})

	t.Run("GetUser - Empty Token", func(t *testing.T) {
		_, err := service.GetUser(ctx, "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "VALIDATION_FAILED")
	})

	t.Run("GetUser - Mock Token", func(t *testing.T) {
		_, err := service.GetUser(ctx, "mock_access_token")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "MOCK_DATA_DETECTED")
	})
}

// TestSupabaseAuthService_ContextHandling tests context handling
// Task 2.3: Test context timeout and cancellation
func TestSupabaseAuthService_ContextHandling(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	cfg := &config.Config{
		Supabase: config.SupabaseConfig{
			URL:     "https://project.supabase.co",
			AnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb2plY3QiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MDk5NTIwMCwiZXhwIjoxOTU2MzcxMjAwfQ",
		},
	}
	service, err := NewSupabaseAuthService(cfg, logger)
	assert.NoError(t, err)
	assert.NotNil(t, service)

	t.Run("Context Timeout", func(t *testing.T) {
		// Create context with very short timeout
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Nanosecond)
		defer cancel()

		// Wait for context to timeout
		time.Sleep(1 * time.Millisecond)

		_, err := service.SignInWithEmail(ctx, "<EMAIL>", "password123")
		assert.Error(t, err)
		// Context timeout should result in network error
		assert.Contains(t, err.Error(), "NETWORK_ERROR")
	})

	t.Run("Context Cancellation", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		_, err := service.SignInWithEmail(ctx, "<EMAIL>", "password123")
		assert.Error(t, err)
		// Context cancellation should result in network error
		assert.Contains(t, err.Error(), "NETWORK_ERROR")
	})
}

// TestSupabaseAuthService_ErrorHandling tests error handling
// Task 2.3: Test error handling and logging
func TestSupabaseAuthService_ErrorHandling(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	cfg := &config.Config{
		Supabase: config.SupabaseConfig{
			URL:     "https://invalid-url-that-does-not-exist.com",
			AnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb2plY3QiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MDk5NTIwMCwiZXhwIjoxOTU2MzcxMjAwfQ",
		},
	}
	service, err := NewSupabaseAuthService(cfg, logger)
	assert.NoError(t, err)
	assert.NotNil(t, service)

	ctx := context.Background()

	t.Run("Network Error Handling", func(t *testing.T) {
		_, err := service.SignInWithEmail(ctx, "<EMAIL>", "password123")
		assert.Error(t, err)
		// Should handle network errors gracefully
		// Could be NETWORK_ERROR or INTERNAL_ERROR depending on response
		assert.True(t,
			strings.Contains(err.Error(), "NETWORK_ERROR") ||
				strings.Contains(err.Error(), "INTERNAL_ERROR"))
	})
}

// TestSupabaseAuthService_ConfigurationValidation tests configuration validation
// Task 2.3: Test configuration validation and defaults
func TestSupabaseAuthService_ConfigurationValidation(t *testing.T) {
	logger, _ := zap.NewDevelopment()

	t.Run("Mock Data Detection in URL", func(t *testing.T) {
		cfg := &config.Config{
			Supabase: config.SupabaseConfig{
				URL:     "https://mock-project.supabase.co",
				AnonKey: "valid-anon-key",
			},
		}
		service, err := NewSupabaseAuthService(cfg, logger)
		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.Error(), "MOCK_DATA_DETECTED")
	})

	t.Run("Mock Data Detection in Anon Key", func(t *testing.T) {
		cfg := &config.Config{
			Supabase: config.SupabaseConfig{
				URL:     "https://project.supabase.co",
				AnonKey: "mock_anon_key",
			},
		}
		service, err := NewSupabaseAuthService(cfg, logger)
		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.Error(), "MOCK_DATA_DETECTED")
	})

	t.Run("Invalid URL Format", func(t *testing.T) {
		cfg := &config.Config{
			Supabase: config.SupabaseConfig{
				URL:     "invalid-url",
				AnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb2plY3QiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MDk5NTIwMCwiZXhwIjoxOTU2MzcxMjAwfQ",
			},
		}
		service, err := NewSupabaseAuthService(cfg, logger)
		assert.Error(t, err)
		assert.Nil(t, service)
		if err != nil {
			assert.Contains(t, err.Error(), "CONFIGURATION_ERROR")
		}
	})
}

// TestSupabaseAuthService_HTTPClientConfiguration tests HTTP client setup
// Task 2.3: Test HTTP client configuration and timeouts
func TestSupabaseAuthService_HTTPClientConfiguration(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	cfg := &config.Config{
		Supabase: config.SupabaseConfig{
			URL:     "https://project.supabase.co",
			AnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb2plY3QiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MDk5NTIwMCwiZXhwIjoxOTU2MzcxMjAwfQ",
		},
	}
	service, err := NewSupabaseAuthService(cfg, logger)
	assert.NoError(t, err)
	assert.NotNil(t, service)

	// Test that HTTP client is properly configured
	assert.NotNil(t, service.httpClient)
	assert.Equal(t, 30*time.Second, service.httpClient.Timeout)
}

// TestSupabaseAuthService_LoggingAndMetrics tests logging and metrics
// Task 2.3: Test logging and metrics collection
func TestSupabaseAuthService_LoggingAndMetrics(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	cfg := &config.Config{
		Supabase: config.SupabaseConfig{
			URL:     "https://project.supabase.co",
			AnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb2plY3QiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MDk5NTIwMCwiZXhwIjoxOTU2MzcxMjAwfQ",
		},
	}
	service, err := NewSupabaseAuthService(cfg, logger)
	assert.NoError(t, err)
	assert.NotNil(t, service)

	// Test that logger is properly set
	assert.NotNil(t, service.logger)
	assert.Equal(t, logger, service.logger)
}
