package services

import (
	"crypto/rsa"
	"fmt"
	"log"
	"strings"
	"time"

	"carnow-backend/internal/config"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// JWTService handles JWT token generation and validation with enhanced features
// Task 1.2: Enhanced JWT service with better error handling, logging, and token management
type JWTService struct {
	config        *config.Config
	publicKey     *rsa.PublicKey
	privateKey    *rsa.PrivateKey
	keyID         string
	revokedTokens map[string]time.Time // In-memory token blacklist (use Redis in production)
}

// Claims represents the JWT claims structure
type Claims struct {
	UserID    string `json:"user_id"`
	Email     string `json:"email"`
	FullName  string `json:"full_name,omitempty"`
	Role      string `json:"role,omitempty"`
	TokenType string `json:"token_type,omitempty"` // "access" or "refresh"
	ClientIP  string `json:"client_ip,omitempty"`  // Track client IP for security
	jwt.RegisteredClaims
}

// TokenPair represents access and refresh token pair
type TokenPair struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	TokenType    string    `json:"token_type"`
}

// TokenValidationResult represents the result of token validation
type TokenValidationResult struct {
	Claims    *Claims `json:"claims"`
	Valid     bool    `json:"valid"`
	Error     string  `json:"error,omitempty"`
	ErrorCode string  `json:"error_code,omitempty"`
}

// NewJWTService creates a new JWT service with enhanced initialization
// Task 1.2: Enhanced service initialization with validation and setup
func NewJWTService(cfg *config.Config) (*JWTService, error) {
	// Validate configuration
	if cfg == nil {
		return nil, fmt.Errorf("configuration cannot be nil")
	}

	if cfg.JWT.Secret == "" {
		return nil, fmt.Errorf("JWT secret is required")
	}

	if len(cfg.JWT.Secret) < 32 {
		return nil, fmt.Errorf("JWT secret must be at least 32 characters long")
	}

	// Generate unique key ID for this instance
	keyID := fmt.Sprintf("carnow-jwt-key-%d", time.Now().Unix())

	service := &JWTService{
		config:        cfg,
		keyID:         keyID,
		revokedTokens: make(map[string]time.Time),
	}

	log.Printf("✅ JWT Service: Initialized with key ID: %s", keyID)
	log.Printf("✅ JWT Service: Access token expiry: %v", cfg.JWT.ExpiresIn)
	log.Printf("✅ JWT Service: Refresh token expiry: %v", cfg.JWT.RefreshExpiresIn)

	return service, nil
}

// GenerateTokens generates access and refresh tokens for a user with enhanced validation
// Task 1.2: Enhanced token generation with better validation and logging
func (s *JWTService) GenerateTokens(userID, email, fullName, role string) (accessToken, refreshToken string, err error) {
	return s.GenerateTokensWithClientIP(userID, email, fullName, role, "")
}

// GenerateTokensWithClientIP generates tokens with client IP tracking for enhanced security
// Task 1.2: Enhanced token generation with client IP tracking
func (s *JWTService) GenerateTokensWithClientIP(userID, email, fullName, role, clientIP string) (accessToken, refreshToken string, err error) {
	// Validate input parameters
	if err := s.validateTokenInputs(userID, email, fullName, role); err != nil {
		return "", "", fmt.Errorf("token generation validation failed: %w", err)
	}

	now := time.Now()

	// Create access token with enhanced claims
	accessClaims := Claims{
		UserID:    userID,
		Email:     email,
		FullName:  fullName,
		Role:      role,
		TokenType: "access",
		ClientIP:  clientIP,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        uuid.New().String(), // Unique token ID for tracking
			ExpiresAt: jwt.NewNumericDate(now.Add(s.config.JWT.ExpiresIn)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    s.config.JWT.Issuer,
			Subject:   userID,
			Audience:  []string{s.config.JWT.Audience},
		},
	}

	// Sign access token with key ID
	accessTokenObj := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	accessTokenObj.Header["kid"] = s.keyID

	accessToken, err = accessTokenObj.SignedString([]byte(s.config.JWT.Secret))
	if err != nil {
		log.Printf("❌ JWT Service: Failed to generate access token for user %s: %v", userID, err)
		return "", "", fmt.Errorf("failed to generate access token: %w", err)
	}

	// Create refresh token with essential claims (include fullName and role for refresh functionality)
	refreshClaims := Claims{
		UserID:    userID,
		Email:     email,
		FullName:  fullName,
		Role:      role,
		TokenType: "refresh",
		ClientIP:  clientIP,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        uuid.New().String(), // Unique token ID for tracking
			ExpiresAt: jwt.NewNumericDate(now.Add(s.config.JWT.RefreshExpiresIn)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    s.config.JWT.Issuer,
			Subject:   userID,
			Audience:  []string{s.config.JWT.Audience},
		},
	}

	// Sign refresh token with key ID
	refreshTokenObj := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenObj.Header["kid"] = s.keyID

	refreshToken, err = refreshTokenObj.SignedString([]byte(s.config.JWT.Secret))
	if err != nil {
		log.Printf("❌ JWT Service: Failed to generate refresh token for user %s: %v", userID, err)
		return "", "", fmt.Errorf("failed to generate refresh token: %w", err)
	}

	log.Printf("✅ JWT Service: Generated token pair for user %s (%s) from IP %s", userID, email, clientIP)
	return accessToken, refreshToken, nil
}

// ValidateAccessToken validates an access token and returns the claims
func (s *JWTService) ValidateAccessToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.config.JWT.Secret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse access token: %w", err)
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid access token")
}

// ValidateRefreshToken validates a refresh token
func (s *JWTService) ValidateRefreshToken(tokenString string) (*jwt.RegisteredClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.config.JWT.Secret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse refresh token: %w", err)
	}

	if claims, ok := token.Claims.(*jwt.RegisteredClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid refresh token")
}

// validateTokenInputs validates input parameters for token generation
// Task 1.2: Enhanced input validation for token generation
func (s *JWTService) validateTokenInputs(userID, email, fullName, role string) error {
	// Validate required fields
	if strings.TrimSpace(userID) == "" {
		return fmt.Errorf("user ID cannot be empty")
	}

	if strings.TrimSpace(email) == "" {
		return fmt.Errorf("email cannot be empty")
	}

	if strings.TrimSpace(fullName) == "" {
		return fmt.Errorf("full name cannot be empty")
	}

	if strings.TrimSpace(role) == "" {
		return fmt.Errorf("role cannot be empty")
	}

	// Validate email format (basic validation)
	if !strings.Contains(email, "@") || !strings.Contains(email, ".") {
		return fmt.Errorf("invalid email format: %s", email)
	}

	// Check for mock data patterns (Forever Plan compliance)
	mockPatterns := []string{"mock_", "test_", "fake_", "sample_", "demo_"}
	for _, pattern := range mockPatterns {
		if strings.Contains(strings.ToLower(userID), pattern) {
			return fmt.Errorf("mock data detected in user ID: %s", userID)
		}
		if strings.Contains(strings.ToLower(email), pattern) {
			return fmt.Errorf("mock data detected in email: %s", email)
		}
		if strings.Contains(strings.ToLower(fullName), pattern) {
			return fmt.Errorf("mock data detected in full name: %s", fullName)
		}
	}

	return nil
}

// ValidateTokenWithDetails validates a token and returns detailed result
// Task 1.2: Enhanced token validation with detailed error information
func (s *JWTService) ValidateTokenWithDetails(tokenString string) *TokenValidationResult {
	result := &TokenValidationResult{
		Valid: false,
	}

	// Basic format validation
	if strings.TrimSpace(tokenString) == "" {
		result.Error = "Token cannot be empty"
		result.ErrorCode = "TOKEN_EMPTY"
		return result
	}

	// Check if token is revoked
	if s.isTokenRevoked(tokenString) {
		result.Error = "Token has been revoked"
		result.ErrorCode = "TOKEN_REVOKED"
		return result
	}

	// Parse and validate token
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.config.JWT.Secret), nil
	})

	if err != nil {
		result.Error = err.Error()

		// Categorize error types
		switch {
		case strings.Contains(err.Error(), "expired"):
			result.ErrorCode = "TOKEN_EXPIRED"
		case strings.Contains(err.Error(), "invalid"):
			result.ErrorCode = "TOKEN_INVALID"
		case strings.Contains(err.Error(), "malformed"):
			result.ErrorCode = "TOKEN_MALFORMED"
		default:
			result.ErrorCode = "TOKEN_VALIDATION_FAILED"
		}

		log.Printf("❌ JWT Service: Token validation failed: %v", err)
		return result
	}

	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		result.Error = "Invalid token claims"
		result.ErrorCode = "TOKEN_CLAIMS_INVALID"
		return result
	}

	// Additional validation for real data (Forever Plan compliance)
	if err := s.validateClaimsRealData(claims); err != nil {
		result.Error = err.Error()
		result.ErrorCode = "TOKEN_MOCK_DATA_DETECTED"
		return result
	}

	result.Claims = claims
	result.Valid = true

	log.Printf("✅ JWT Service: Token validated successfully for user %s (%s)", claims.UserID, claims.Email)
	return result
}

// validateClaimsRealData validates that token claims contain real data only
// Task 1.2: Forever Plan compliance - ensure real data only in tokens
func (s *JWTService) validateClaimsRealData(claims *Claims) error {
	// Check for mock data patterns
	mockPatterns := []string{"mock_", "test_", "fake_", "sample_", "demo_"}

	for _, pattern := range mockPatterns {
		if strings.Contains(strings.ToLower(claims.UserID), pattern) {
			return fmt.Errorf("mock data detected in token user ID: %s", claims.UserID)
		}
		if strings.Contains(strings.ToLower(claims.Email), pattern) {
			return fmt.Errorf("mock data detected in token email: %s", claims.Email)
		}
		if strings.Contains(strings.ToLower(claims.FullName), pattern) {
			return fmt.Errorf("mock data detected in token full name: %s", claims.FullName)
		}
	}

	return nil
}

// isTokenRevoked checks if a token has been revoked
// Task 1.2: Token revocation support for enhanced security
func (s *JWTService) isTokenRevoked(tokenString string) bool {
	// In production, this should check Redis or database
	// For now, using in-memory map
	if revokedAt, exists := s.revokedTokens[tokenString]; exists {
		// Clean up expired revoked tokens
		if time.Now().After(revokedAt.Add(24 * time.Hour)) {
			delete(s.revokedTokens, tokenString)
			return false
		}
		return true
	}
	return false
}

// RevokeToken adds a token to the revocation list
// Task 1.2: Token revocation functionality for security
func (s *JWTService) RevokeToken(tokenString string) error {
	if strings.TrimSpace(tokenString) == "" {
		return fmt.Errorf("token cannot be empty")
	}

	// Add to revoked tokens list
	s.revokedTokens[tokenString] = time.Now()

	log.Printf("🔒 JWT Service: Token revoked (total revoked: %d)", len(s.revokedTokens))
	return nil
}

// RefreshToken generates a new access token using a valid refresh token
// Task 1.2: Enhanced refresh token functionality
func (s *JWTService) RefreshToken(refreshTokenString string) (*TokenPair, error) {
	// Validate refresh token
	result := s.ValidateTokenWithDetails(refreshTokenString)
	if !result.Valid {
		return nil, fmt.Errorf("invalid refresh token: %s", result.Error)
	}

	claims := result.Claims

	// Ensure this is a refresh token
	if claims.TokenType != "refresh" {
		return nil, fmt.Errorf("token is not a refresh token")
	}

	// Generate new token pair (use default values for missing fields in refresh token)
	fullName := claims.FullName
	if fullName == "" {
		fullName = "User" // Default value for refresh tokens that don't have full name
	}

	role := claims.Role
	if role == "" {
		role = "user" // Default role for refresh tokens
	}

	accessToken, newRefreshToken, err := s.GenerateTokensWithClientIP(
		claims.UserID,
		claims.Email,
		fullName,
		role,
		claims.ClientIP,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new tokens: %w", err)
	}

	// Revoke old refresh token
	if err := s.RevokeToken(refreshTokenString); err != nil {
		log.Printf("⚠️ JWT Service: Failed to revoke old refresh token: %v", err)
	}

	tokenPair := &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		ExpiresAt:    time.Now().Add(s.config.JWT.ExpiresIn),
		TokenType:    "Bearer",
	}

	log.Printf("🔄 JWT Service: Refreshed tokens for user %s (%s)", claims.UserID, claims.Email)
	return tokenPair, nil
}
