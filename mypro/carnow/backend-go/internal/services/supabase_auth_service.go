package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/shared/errors"

	"go.uber.org/zap"
)

// SupabaseAuthService provides authentication operations using Supabase
// Task 2.1: Complete Supabase authentication service with proper error handling
type SupabaseAuthService struct {
	config     *config.Config
	logger     *zap.Logger
	httpClient *http.Client
	baseURL    string
	apiKey     string
}

// AuthRequest represents authentication request data
type AuthRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=6"`
}

// AuthResponse represents Supabase authentication response
type AuthResponse struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresIn    int       `json:"expires_in"`
	TokenType    string    `json:"token_type"`
	User         *User     `json:"user"`
	ExpiresAt    time.Time `json:"expires_at"`
}

// User represents user data from Supabase
type User struct {
	ID                 string                 `json:"id"`
	Email              string                 `json:"email"`
	EmailConfirmed     bool                   `json:"email_confirmed_at,omitempty"`
	Phone              string                 `json:"phone,omitempty"`
	CreatedAt          time.Time              `json:"created_at"`
	UpdatedAt          time.Time              `json:"updated_at"`
	LastSignInAt       *time.Time             `json:"last_sign_in_at,omitempty"`
	Role               string                 `json:"role,omitempty"`
	UserMetadata       map[string]interface{} `json:"user_metadata,omitempty"`
	AppMetadata        map[string]interface{} `json:"app_metadata,omitempty"`
	Aud                string                 `json:"aud,omitempty"`
	ConfirmationSentAt *time.Time             `json:"confirmation_sent_at,omitempty"`
}

// RefreshTokenRequest represents refresh token request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// SignOutRequest represents sign out request
type SignOutRequest struct {
	AccessToken string `json:"access_token,omitempty"`
}

// SupabaseErrorResponse represents error response from Supabase
type SupabaseErrorResponse struct {
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
	Message          string `json:"message"`
	Code             int    `json:"code"`
}

// NewSupabaseAuthService creates a new Supabase authentication service
// Task 2.1: Initialize service with proper configuration and validation
func NewSupabaseAuthService(cfg *config.Config, logger *zap.Logger) (*SupabaseAuthService, error) {
	if cfg == nil {
		return nil, errors.ErrInternalError("configuration is required")
	}

	if logger == nil {
		return nil, errors.ErrInternalError("logger is required")
	}

	// Validate Supabase configuration
	if cfg.Supabase.URL == "" {
		return nil, errors.NewAuthError(errors.ErrCodeConfigurationError, "Supabase URL is required")
	}

	// Validate URL format
	if !strings.HasPrefix(cfg.Supabase.URL, "http://") && !strings.HasPrefix(cfg.Supabase.URL, "https://") {
		return nil, errors.NewAuthError(errors.ErrCodeConfigurationError, "Supabase URL must be a valid HTTP/HTTPS URL")
	}

	if cfg.Supabase.AnonKey == "" {
		return nil, errors.NewAuthError(errors.ErrCodeConfigurationError, "Supabase anon key is required")
	}

	// Check for mock data (Forever Plan compliance)
	if containsMockData(cfg.Supabase.URL) || containsMockData(cfg.Supabase.AnonKey) {
		return nil, errors.ErrMockDataDetected("Supabase configuration")
	}

	// Create HTTP client with timeout
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        10,
			IdleConnTimeout:     30 * time.Second,
			DisableCompression:  false,
			MaxIdleConnsPerHost: 10,
		},
	}

	service := &SupabaseAuthService{
		config:     cfg,
		logger:     logger,
		httpClient: httpClient,
		baseURL:    strings.TrimSuffix(cfg.Supabase.URL, "/"),
		apiKey:     cfg.Supabase.AnonKey,
	}

	logger.Info("Supabase Auth Service initialized",
		zap.String("base_url", service.baseURL),
		zap.String("service", "supabase_auth"),
	)

	return service, nil
}

// SignInWithEmail authenticates user with email and password
// Task 2.1: Implement email/password authentication with proper error handling
func (s *SupabaseAuthService) SignInWithEmail(ctx context.Context, email, password string) (*AuthResponse, error) {
	s.logger.Info("Attempting sign in with email",
		zap.String("email", email),
		zap.String("operation", "sign_in"),
	)

	// Validate input
	if email == "" {
		return nil, errors.NewAuthError(errors.ErrCodeValidationFailed, "email is required")
	}
	if password == "" {
		return nil, errors.NewAuthError(errors.ErrCodeValidationFailed, "password is required")
	}

	// Check for mock data (Forever Plan compliance)
	if containsMockData(email) || containsMockData(password) {
		return nil, errors.ErrMockDataDetected("authentication credentials")
	}

	// Prepare request
	authReq := AuthRequest{
		Email:    email,
		Password: password,
	}

	reqBody, err := json.Marshal(authReq)
	if err != nil {
		s.logger.Error("Failed to marshal auth request", zap.Error(err))
		return nil, errors.ErrInternalError("failed to prepare authentication request")
	}

	// Make request to Supabase
	url := fmt.Sprintf("%s/auth/v1/token?grant_type=password", s.baseURL)
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		s.logger.Error("Failed to create auth request", zap.Error(err))
		return nil, errors.ErrInternalError("failed to create authentication request")
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("apikey", s.apiKey)

	// Execute request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		s.logger.Error("Auth request failed", zap.Error(err))
		return nil, errors.NewAuthError(errors.ErrCodeNetworkError, "authentication request failed")
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		s.logger.Error("Failed to read auth response", zap.Error(err))
		return nil, errors.ErrInternalError("failed to read authentication response")
	}

	// Handle error responses
	if resp.StatusCode != http.StatusOK {
		return nil, s.handleSupabaseError(resp.StatusCode, body)
	}

	// Parse successful response
	var authResp AuthResponse
	if err := json.Unmarshal(body, &authResp); err != nil {
		s.logger.Error("Failed to parse auth response", zap.Error(err))
		return nil, errors.ErrInternalError("failed to parse authentication response")
	}

	// Validate response data (Forever Plan compliance)
	if err := s.validateAuthResponse(&authResp); err != nil {
		return nil, err
	}

	// Set expiration time
	authResp.ExpiresAt = time.Now().Add(time.Duration(authResp.ExpiresIn) * time.Second)

	s.logger.Info("Sign in successful",
		zap.String("user_id", authResp.User.ID),
		zap.String("email", authResp.User.Email),
		zap.Time("expires_at", authResp.ExpiresAt),
	)

	return &authResp, nil
}

// SignOut signs out the user and invalidates the token
// Task 2.1: Implement sign out with proper token invalidation
func (s *SupabaseAuthService) SignOut(ctx context.Context, accessToken string) error {
	s.logger.Info("Attempting sign out", zap.String("operation", "sign_out"))

	// Validate input
	if accessToken == "" {
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "access token is required")
	}

	// Check for mock data (Forever Plan compliance)
	if containsMockData(accessToken) {
		return errors.ErrMockDataDetected("access token")
	}

	// Prepare request
	url := fmt.Sprintf("%s/auth/v1/logout", s.baseURL)
	req, err := http.NewRequestWithContext(ctx, "POST", url, nil)
	if err != nil {
		s.logger.Error("Failed to create logout request", zap.Error(err))
		return errors.ErrInternalError("failed to create logout request")
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("apikey", s.apiKey)
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// Execute request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		s.logger.Error("Logout request failed", zap.Error(err))
		return errors.NewAuthError(errors.ErrCodeNetworkError, "logout request failed")
	}
	defer resp.Body.Close()

	// Handle response
	if resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return s.handleSupabaseError(resp.StatusCode, body)
	}

	s.logger.Info("Sign out successful", zap.String("operation", "sign_out"))
	return nil
}

// RefreshToken refreshes the access token using refresh token
// Task 2.1: Implement token refresh with proper error handling
func (s *SupabaseAuthService) RefreshToken(ctx context.Context, refreshToken string) (*AuthResponse, error) {
	s.logger.Info("Attempting token refresh", zap.String("operation", "refresh_token"))

	// Validate input
	if refreshToken == "" {
		return nil, errors.NewAuthError(errors.ErrCodeValidationFailed, "refresh token is required")
	}

	// Check for mock data (Forever Plan compliance)
	if containsMockData(refreshToken) {
		return nil, errors.ErrMockDataDetected("refresh token")
	}

	// Prepare request
	refreshReq := RefreshTokenRequest{
		RefreshToken: refreshToken,
	}

	reqBody, err := json.Marshal(refreshReq)
	if err != nil {
		s.logger.Error("Failed to marshal refresh request", zap.Error(err))
		return nil, errors.ErrInternalError("failed to prepare refresh request")
	}

	// Make request to Supabase
	url := fmt.Sprintf("%s/auth/v1/token?grant_type=refresh_token", s.baseURL)
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		s.logger.Error("Failed to create refresh request", zap.Error(err))
		return nil, errors.ErrInternalError("failed to create refresh request")
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("apikey", s.apiKey)

	// Execute request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		s.logger.Error("Refresh request failed", zap.Error(err))
		return nil, errors.NewAuthError(errors.ErrCodeNetworkError, "token refresh request failed")
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		s.logger.Error("Failed to read refresh response", zap.Error(err))
		return nil, errors.ErrInternalError("failed to read refresh response")
	}

	// Handle error responses
	if resp.StatusCode != http.StatusOK {
		return nil, s.handleSupabaseError(resp.StatusCode, body)
	}

	// Parse successful response
	var authResp AuthResponse
	if err := json.Unmarshal(body, &authResp); err != nil {
		s.logger.Error("Failed to parse refresh response", zap.Error(err))
		return nil, errors.ErrInternalError("failed to parse refresh response")
	}

	// Validate response data (Forever Plan compliance)
	if err := s.validateAuthResponse(&authResp); err != nil {
		return nil, err
	}

	// Set expiration time
	authResp.ExpiresAt = time.Now().Add(time.Duration(authResp.ExpiresIn) * time.Second)

	s.logger.Info("Token refresh successful",
		zap.String("user_id", authResp.User.ID),
		zap.Time("expires_at", authResp.ExpiresAt),
	)

	return &authResp, nil
}

// GetUser retrieves user information using access token
// Task 2.1: Implement user retrieval with proper error handling
func (s *SupabaseAuthService) GetUser(ctx context.Context, accessToken string) (*User, error) {
	s.logger.Info("Attempting to get user", zap.String("operation", "get_user"))

	// Validate input
	if accessToken == "" {
		return nil, errors.NewAuthError(errors.ErrCodeValidationFailed, "access token is required")
	}

	// Check for mock data (Forever Plan compliance)
	if containsMockData(accessToken) {
		return nil, errors.ErrMockDataDetected("access token")
	}

	// Make request to Supabase
	url := fmt.Sprintf("%s/auth/v1/user", s.baseURL)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		s.logger.Error("Failed to create get user request", zap.Error(err))
		return nil, errors.ErrInternalError("failed to create get user request")
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("apikey", s.apiKey)
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// Execute request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		s.logger.Error("Get user request failed", zap.Error(err))
		return nil, errors.NewAuthError(errors.ErrCodeNetworkError, "get user request failed")
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		s.logger.Error("Failed to read get user response", zap.Error(err))
		return nil, errors.ErrInternalError("failed to read get user response")
	}

	// Handle error responses
	if resp.StatusCode != http.StatusOK {
		return nil, s.handleSupabaseError(resp.StatusCode, body)
	}

	// Parse successful response
	var user User
	if err := json.Unmarshal(body, &user); err != nil {
		s.logger.Error("Failed to parse get user response", zap.Error(err))
		return nil, errors.ErrInternalError("failed to parse get user response")
	}

	// Validate user data (Forever Plan compliance)
	if err := s.validateUser(&user); err != nil {
		return nil, err
	}

	s.logger.Info("Get user successful",
		zap.String("user_id", user.ID),
		zap.String("email", user.Email),
	)

	return &user, nil
}

// handleSupabaseError converts Supabase errors to AuthError
// Task 2.1: Proper error handling and conversion
func (s *SupabaseAuthService) handleSupabaseError(statusCode int, body []byte) error {
	var supabaseErr SupabaseErrorResponse
	if err := json.Unmarshal(body, &supabaseErr); err != nil {
		s.logger.Error("Failed to parse Supabase error response", zap.Error(err))
		return errors.ErrInternalError("failed to parse error response")
	}

	s.logger.Error("Supabase API error",
		zap.Int("status_code", statusCode),
		zap.String("error", supabaseErr.Error),
		zap.String("error_description", supabaseErr.ErrorDescription),
		zap.String("message", supabaseErr.Message),
	)

	// Map Supabase errors to AuthError
	switch statusCode {
	case http.StatusBadRequest:
		if strings.Contains(supabaseErr.Error, "invalid_grant") {
			return errors.ErrInvalidCredentials()
		}
		if strings.Contains(supabaseErr.Error, "email_not_confirmed") {
			return errors.NewAuthError(errors.ErrCodeUserDisabled, "Email not confirmed", supabaseErr.ErrorDescription)
		}
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "Invalid request", supabaseErr.ErrorDescription)

	case http.StatusUnauthorized:
		if strings.Contains(supabaseErr.Error, "invalid_token") {
			return errors.ErrTokenInvalid(supabaseErr.ErrorDescription)
		}
		if strings.Contains(supabaseErr.Error, "token_expired") {
			return errors.ErrTokenExpired()
		}
		return errors.ErrInvalidCredentials()

	case http.StatusForbidden:
		return errors.ErrAccessDenied("authentication service")

	case http.StatusNotFound:
		return errors.ErrUserNotFound()

	case http.StatusTooManyRequests:
		return errors.ErrRateLimitExceeded(5 * time.Minute)

	case http.StatusInternalServerError, http.StatusBadGateway, http.StatusServiceUnavailable:
		return errors.ErrServiceUnavailable("Supabase")

	default:
		return errors.ErrInternalError(fmt.Sprintf("Supabase error: %s", supabaseErr.Message))
	}
}

// validateAuthResponse validates authentication response data
// Task 2.1: Ensure real data only (Forever Plan compliance)
func (s *SupabaseAuthService) validateAuthResponse(authResp *AuthResponse) error {
	if authResp == nil {
		return errors.ErrInternalError("authentication response is nil")
	}

	if authResp.AccessToken == "" {
		return errors.ErrInternalError("access token is missing from response")
	}

	if authResp.User == nil {
		return errors.ErrInternalError("user data is missing from response")
	}

	// Check for mock data in response (Forever Plan compliance)
	if containsMockData(authResp.AccessToken) {
		return errors.ErrMockDataDetected("access token in response")
	}

	if containsMockData(authResp.RefreshToken) {
		return errors.ErrMockDataDetected("refresh token in response")
	}

	return s.validateUser(authResp.User)
}

// validateUser validates user data
// Task 2.1: Ensure real user data only (Forever Plan compliance)
func (s *SupabaseAuthService) validateUser(user *User) error {
	if user == nil {
		return errors.ErrInternalError("user data is nil")
	}

	if user.ID == "" {
		return errors.ErrInternalError("user ID is missing")
	}

	if user.Email == "" {
		return errors.ErrInternalError("user email is missing")
	}

	// Check for mock data in user fields (Forever Plan compliance)
	mockFields := []struct {
		field string
		value string
	}{
		{"user_id", user.ID},
		{"email", user.Email},
		{"role", user.Role},
	}

	for _, field := range mockFields {
		if containsMockData(field.value) {
			return errors.ErrMockDataDetected(fmt.Sprintf("user %s", field.field))
		}
	}

	return nil
}

// containsMockData checks if a string contains mock data patterns
// Task 2.1: Forever Plan compliance - detect mock data
func containsMockData(value string) bool {
	if len(value) > 200 { // Skip very long values like JWT tokens
		return false
	}

	lowerValue := strings.ToLower(value)
	mockPatterns := []string{
		"mock", "test", "fake", "sample", "demo", "example",
		"localhost", "127.0.0.1", "0.0.0.0",
		"test@test", "fake@fake", "demo@demo",
		"placeholder", "dummy", "temp",
	}

	for _, pattern := range mockPatterns {
		if strings.Contains(lowerValue, pattern) {
			return true
		}
	}

	return false
}

// Close closes the service and cleans up resources
func (s *SupabaseAuthService) Close() error {
	s.logger.Info("Closing Supabase Auth Service")
	if s.httpClient != nil {
		s.httpClient.CloseIdleConnections()
	}
	return nil
}
