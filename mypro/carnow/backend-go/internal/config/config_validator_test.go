package config

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// createTestConfig creates a valid test configuration
func createTestConfig() *Config {
	return &Config{
		App: AppConfig{
			Name:        "CarNow Backend Test",
			Version:     "1.0.0",
			Environment: "development",
			Debug:       true,
			Timezone:    "Africa/Tripoli",
		},
		Server: ServerConfig{
			Host:           "localhost",
			Port:           8080,
			ReadTimeout:    30 * time.Second,
			WriteTimeout:   30 * time.Second,
			IdleTimeout:    60 * time.Second,
			MaxHeaderBytes: 1048576,
		},
		Database: DatabaseConfig{
			Host:            "localhost",
			Port:            5432,
			Username:        "postgres",
			Password:        "password",
			Database:        "carnow_test",
			SSLMode:         "disable",
			MaxOpenConns:    10,
			MaxIdleConns:    5,
			ConnMaxLifetime: time.Hour,
		},
		Supabase: SupabaseConfig{
			URL:            "https://lpxtghyvxuenyyisrrro.supabase.co",
			<PERSON><PERSON><PERSON><PERSON>:        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU2NzI4MDAsImV4cCI6MjA1MTI0ODgwMH0.test",
			ServiceRoleKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNTY3MjgwMCwiZXhwIjoyMDUxMjQ4ODAwfQ.test",
			JWTSecret:      "test-jwt-secret-that-is-at-least-32-characters-long",
			ProjectRef:     "lpxtghyvxuenyyisrrro",
		},
		JWT: JWTConfig{
			Secret:           "test-jwt-secret-that-is-at-least-32-characters-long",
			Algorithm:        "HS256",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-users",
		},
		Security: SecurityConfig{
			EncryptionKey:     "test-encryption-key-32-characters",
			RateLimitRequests: 100,
			RateLimitWindow:   "1m",
			RateLimit: RateLimitConfig{
				LoginAttempts:    5,
				RegisterAttempts: 3,
				WindowMinutes:    15,
			},
		},
		Logging: LoggingConfig{
			Level:  "info",
			Format: "json",
		},
	}
}

// TestNewConfigValidator tests the validator creation
func TestNewConfigValidator(t *testing.T) {
	config := createTestConfig()
	validator := NewConfigValidator(config)

	assert.NotNil(t, validator)
	assert.Equal(t, config, validator.cfg)
}

// TestValidateEnvironment tests environment validation
func TestValidateEnvironment(t *testing.T) {
	tests := []struct {
		name        string
		environment string
		expectError bool
	}{
		{
			name:        "Valid Development Environment",
			environment: "development",
			expectError: false,
		},
		{
			name:        "Valid Staging Environment",
			environment: "staging",
			expectError: false,
		},
		{
			name:        "Valid Production Environment",
			environment: "production",
			expectError: false,
		},
		{
			name:        "Invalid Environment",
			environment: "invalid",
			expectError: true,
		},
		{
			name:        "Empty Environment",
			environment: "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := createTestConfig()
			config.App.Environment = tt.environment

			validator := NewConfigValidator(config)
			err := validator.validateEnvironment()

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestValidateServer tests server configuration validation
func TestValidateServer(t *testing.T) {
	tests := []struct {
		name        string
		port        int
		expectError bool
	}{
		{
			name:        "Valid Port",
			port:        8080,
			expectError: false,
		},
		{
			name:        "Port Too Low",
			port:        0,
			expectError: true,
		},
		{
			name:        "Port Too High",
			port:        70000,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := createTestConfig()
			config.Server.Port = tt.port

			validator := NewConfigValidator(config)
			err := validator.validateServer()

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestValidateSupabaseConfig tests Supabase configuration validation
func TestValidateSupabaseConfig(t *testing.T) {
	tests := []struct {
		name          string
		modifyConfig  func(*Config)
		expectError   bool
		errorContains string
	}{
		{
			name:         "Valid Supabase Config",
			modifyConfig: func(c *Config) {},
			expectError:  false,
		},
		{
			name: "Empty URL",
			modifyConfig: func(c *Config) {
				c.Supabase.URL = ""
			},
			expectError:   true,
			errorContains: "Supabase URL is required",
		},
		{
			name: "Invalid URL Format",
			modifyConfig: func(c *Config) {
				c.Supabase.URL = "invalid-url"
			},
			expectError:   true,
			errorContains: "invalid Supabase URL",
		},
		{
			name: "Non-Supabase Domain",
			modifyConfig: func(c *Config) {
				c.Supabase.URL = "https://example.com"
			},
			expectError:   true,
			errorContains: "must be a valid Supabase domain",
		},
		{
			name: "Invalid Project Reference Length",
			modifyConfig: func(c *Config) {
				c.Supabase.ProjectRef = "short"
			},
			expectError:   true,
			errorContains: "must be 20 characters long",
		},
		{
			name: "Empty Anon Key",
			modifyConfig: func(c *Config) {
				c.Supabase.AnonKey = ""
			},
			expectError:   true,
			errorContains: "Supabase anon key is required",
		},
		{
			name: "Invalid Anon Key Format",
			modifyConfig: func(c *Config) {
				c.Supabase.AnonKey = "invalid-key"
			},
			expectError:   true,
			errorContains: "Supabase anon key appears to be invalid",
		},
		{
			name: "Mock Data in URL",
			modifyConfig: func(c *Config) {
				c.Supabase.URL = "https://mock_project.supabase.co"
			},
			expectError:   true,
			errorContains: "mock data detected in Supabase URL",
		},
		{
			name: "Mock Data in Project Reference",
			modifyConfig: func(c *Config) {
				c.Supabase.ProjectRef = "test_project_ref_123"
			},
			expectError:   true,
			errorContains: "mock data detected in Supabase project reference",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := createTestConfig()
			tt.modifyConfig(config)

			validator := NewConfigValidator(config)
			err := validator.validateSupabaseConfig()

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestValidateJWTConfig tests JWT configuration validation
func TestValidateJWTConfig(t *testing.T) {
	tests := []struct {
		name          string
		modifyConfig  func(*Config)
		expectError   bool
		errorContains string
	}{
		{
			name:         "Valid JWT Config",
			modifyConfig: func(c *Config) {},
			expectError:  false,
		},
		{
			name: "Empty JWT Secret",
			modifyConfig: func(c *Config) {
				c.JWT.Secret = ""
			},
			expectError:   true,
			errorContains: "JWT secret is required",
		},
		{
			name: "Short JWT Secret",
			modifyConfig: func(c *Config) {
				c.JWT.Secret = "short"
			},
			expectError:   true,
			errorContains: "JWT secret must be at least 32 characters",
		},
		{
			name: "Invalid Algorithm",
			modifyConfig: func(c *Config) {
				c.JWT.Algorithm = "INVALID"
			},
			expectError:   true,
			errorContains: "invalid JWT algorithm",
		},
		{
			name: "Expires In Too Short",
			modifyConfig: func(c *Config) {
				c.JWT.ExpiresIn = 1 * time.Minute
			},
			expectError:   true,
			errorContains: "JWT expires_in too short",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := createTestConfig()
			tt.modifyConfig(config)

			validator := NewConfigValidator(config)
			err := validator.validateJWTConfig()

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestValidateAll tests comprehensive validation
func TestValidateAll(t *testing.T) {
	tests := []struct {
		name         string
		modifyConfig func(*Config)
		expectError  bool
	}{
		{
			name:         "Valid Configuration",
			modifyConfig: func(c *Config) {},
			expectError:  false,
		},
		{
			name: "Invalid Environment",
			modifyConfig: func(c *Config) {
				c.App.Environment = "invalid"
			},
			expectError: true,
		},
		{
			name: "Invalid Server Port",
			modifyConfig: func(c *Config) {
				c.Server.Port = 0
			},
			expectError: true,
		},
		{
			name: "Invalid Supabase URL",
			modifyConfig: func(c *Config) {
				c.Supabase.URL = ""
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := createTestConfig()
			tt.modifyConfig(config)

			validator := NewConfigValidator(config)
			err := validator.ValidateAll()

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
