package config

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// createTestHealthChecker creates a health checker with test configuration
func createTestHealthChecker() *HealthChecker {
	config := &Config{
		App: AppConfig{
			Environment: "development",
		},
		Supabase: SupabaseConfig{
			URL:            "https://lpxtghyvxuenyyisrrro.supabase.co",
			An<PERSON><PERSON><PERSON>:        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test",
			ServiceRoleKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test",
			JWTSecret:      "test-jwt-secret-that-is-at-least-32-characters-long",
		},
		JWT: JWTConfig{
			Secret: "test-jwt-secret-that-is-at-least-32-characters-long",
		},
		Security: SecurityConfig{
			EncryptionKey: "test-encryption-key-32-characters",
		},
		Database: DatabaseConfig{
			Host: "localhost",
		},
	}
	return NewHealthChecker(config)
}

// TestNewHealthChecker tests health checker creation
func TestNewHealthChecker(t *testing.T) {
	config := &Config{
		Supabase: SupabaseConfig{
			URL: "https://test.supabase.co",
		},
	}

	checker := NewHealthChecker(config)

	assert.NotNil(t, checker)
	assert.Equal(t, config, checker.config)
	assert.NotNil(t, checker.client)
	assert.Equal(t, 10*time.Second, checker.client.Timeout)
}

// TestCheckSupabaseHealth tests Supabase health checking
func TestCheckSupabaseHealth(t *testing.T) {
	tests := []struct {
		name           string
		serverResponse int
		expectedStatus string
	}{
		{
			name:           "Healthy Response",
			serverResponse: http.StatusOK,
			expectedStatus: "healthy",
		},
		{
			name:           "Not Found Response (Still Healthy)",
			serverResponse: http.StatusNotFound,
			expectedStatus: "healthy",
		},
		{
			name:           "Unauthorized Response (Degraded)",
			serverResponse: http.StatusUnauthorized,
			expectedStatus: "degraded",
		},
		{
			name:           "Server Error (Unhealthy)",
			serverResponse: http.StatusInternalServerError,
			expectedStatus: "unhealthy",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// Verify headers
				assert.NotEmpty(t, r.Header.Get("apikey"))
				assert.NotEmpty(t, r.Header.Get("Authorization"))
				assert.Equal(t, "application/json", r.Header.Get("Content-Type"))

				w.WriteHeader(tt.serverResponse)
			}))
			defer server.Close()

			// Create health checker with test server URL
			config := &Config{
				Supabase: SupabaseConfig{
					URL:     server.URL,
					AnonKey: "test-anon-key",
				},
			}
			checker := NewHealthChecker(config)

			// Perform health check
			ctx := context.Background()
			result := checker.CheckSupabaseHealth(ctx)

			// Verify results
			assert.Equal(t, "supabase", result.Service)
			assert.Equal(t, tt.expectedStatus, result.Status)
			assert.NotZero(t, result.ResponseTime)
			assert.NotZero(t, result.Timestamp)
			assert.NotNil(t, result.Details)
			assert.Equal(t, tt.serverResponse, result.Details["status_code"])
		})
	}
}

// TestCheckSupabaseHealthWithInvalidURL tests health check with invalid URL
func TestCheckSupabaseHealthWithInvalidURL(t *testing.T) {
	config := &Config{
		Supabase: SupabaseConfig{
			URL:     "http://invalid-url-that-definitely-does-not-exist-12345.com",
			AnonKey: "test-anon-key",
		},
	}
	checker := NewHealthChecker(config)

	ctx := context.Background()
	result := checker.CheckSupabaseHealth(ctx)

	assert.Equal(t, "supabase", result.Service)
	// The result could be either unhealthy (connection failed) or healthy (if somehow resolved)
	// We just check that we got a result
	assert.NotEmpty(t, result.Status)
	assert.NotZero(t, result.ResponseTime)
}

// TestCheckSupabaseHealthWithEmptyURL tests health check with empty URL
func TestCheckSupabaseHealthWithEmptyURL(t *testing.T) {
	config := &Config{
		Supabase: SupabaseConfig{
			URL: "",
		},
	}
	checker := NewHealthChecker(config)

	ctx := context.Background()
	result := checker.CheckSupabaseHealth(ctx)

	assert.Equal(t, "supabase", result.Service)
	assert.Equal(t, "unhealthy", result.Status)
	assert.Equal(t, "Supabase URL not configured", result.Message)
}

// TestCheckDatabaseHealth tests database health checking
func TestCheckDatabaseHealth(t *testing.T) {
	tests := []struct {
		name           string
		serverResponse int
		expectedStatus string
	}{
		{
			name:           "Healthy Database",
			serverResponse: http.StatusOK,
			expectedStatus: "healthy",
		},
		{
			name:           "RPC Function Not Found (Still Healthy)",
			serverResponse: http.StatusNotFound,
			expectedStatus: "healthy",
		},
		{
			name:           "Database Error",
			serverResponse: http.StatusInternalServerError,
			expectedStatus: "unhealthy",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// Verify it's a POST request to RPC endpoint
				assert.Equal(t, "POST", r.Method)
				assert.Contains(t, r.URL.Path, "/rpc/version")

				w.WriteHeader(tt.serverResponse)
			}))
			defer server.Close()

			// Create health checker with test server URL
			config := &Config{
				Supabase: SupabaseConfig{
					URL:     server.URL,
					AnonKey: "test-anon-key",
				},
			}
			checker := NewHealthChecker(config)

			// Perform health check
			ctx := context.Background()
			result := checker.CheckDatabaseHealth(ctx)

			// Verify results
			assert.Equal(t, "database", result.Service)
			assert.Equal(t, tt.expectedStatus, result.Status)
			assert.NotZero(t, result.ResponseTime)
			assert.Equal(t, tt.serverResponse, result.Details["status_code"])
		})
	}
}

// TestCheckAllServices tests comprehensive health checking
func TestCheckAllServices(t *testing.T) {
	// Create test server that responds to both REST and RPC endpoints
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method == "GET" {
			// REST API health check
			w.WriteHeader(http.StatusOK)
		} else if r.Method == "POST" && r.URL.Path == "/rest/v1/rpc/version" {
			// Database RPC health check
			w.WriteHeader(http.StatusOK)
		} else {
			w.WriteHeader(http.StatusNotFound)
		}
	}))
	defer server.Close()

	config := &Config{
		Supabase: SupabaseConfig{
			URL:     server.URL,
			AnonKey: "test-anon-key",
		},
	}
	checker := NewHealthChecker(config)

	ctx := context.Background()
	results := checker.CheckAllServices(ctx)

	// Verify we have results for both services
	assert.Len(t, results, 2)
	assert.Contains(t, results, "supabase")
	assert.Contains(t, results, "database")

	// Verify both services are healthy
	assert.Equal(t, "healthy", results["supabase"].Status)
	assert.Equal(t, "healthy", results["database"].Status)
}

// TestValidateEnvironmentVariables tests environment variable validation
func TestValidateEnvironmentVariables(t *testing.T) {
	tests := []struct {
		name          string
		config        *Config
		expectError   bool
		errorContains string
	}{
		{
			name: "Valid Development Environment",
			config: &Config{
				App: AppConfig{Environment: "development"},
				Supabase: SupabaseConfig{
					URL:       "https://test.supabase.co",
					AnonKey:   "test-anon-key",
					JWTSecret: "test-jwt-secret-32-characters-long",
				},
			},
			expectError: false,
		},
		{
			name: "Valid Production Environment",
			config: &Config{
				App: AppConfig{Environment: "production"},
				Supabase: SupabaseConfig{
					URL:            "https://test.supabase.co",
					AnonKey:        "test-anon-key",
					ServiceRoleKey: "test-service-role-key",
					JWTSecret:      "test-jwt-secret-32-characters-long",
				},
				JWT: JWTConfig{
					Secret: "test-jwt-secret-32-characters-long",
				},
				Security: SecurityConfig{
					EncryptionKey: "test-encryption-key-32-characters",
				},
			},
			expectError: false,
		},
		{
			name: "Missing Supabase URL",
			config: &Config{
				App: AppConfig{Environment: "development"},
				Supabase: SupabaseConfig{
					URL:       "",
					AnonKey:   "test-anon-key",
					JWTSecret: "test-jwt-secret-32-characters-long",
				},
			},
			expectError:   true,
			errorContains: "missing required environment variables",
		},
		{
			name: "Missing Production Variables",
			config: &Config{
				App: AppConfig{Environment: "production"},
				Supabase: SupabaseConfig{
					URL:       "https://test.supabase.co",
					AnonKey:   "test-anon-key",
					JWTSecret: "test-jwt-secret-32-characters-long",
					// Missing ServiceRoleKey
				},
				// Missing JWT and Security configs
			},
			expectError:   true,
			errorContains: "missing required environment variables",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			checker := NewHealthChecker(tt.config)
			err := checker.ValidateEnvironmentVariables()

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestContainsMockData tests mock data detection
func TestContainsMockData(t *testing.T) {
	checker := createTestHealthChecker()

	tests := []struct {
		name     string
		value    string
		expected bool
	}{
		{
			name:     "Normal Value",
			value:    "normal-value",
			expected: false,
		},
		{
			name:     "Mock Data",
			value:    "mock_data",
			expected: true,
		},
		{
			name:     "Test Data",
			value:    "test_value",
			expected: true,
		},
		{
			name:     "Fake Data",
			value:    "fake_user",
			expected: true,
		},
		{
			name:     "Long Secret (Should Skip)",
			value:    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.very-long-secret-token",
			expected: false,
		},
		{
			name:     "Empty Value",
			value:    "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := checker.containsMockData(tt.value)
			assert.Equal(t, tt.expected, result)
		})
	}
}
