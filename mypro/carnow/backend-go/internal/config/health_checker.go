package config

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"
)

// HealthChecker provides health check functionality for external services
// Task 1.3: Health checks for Supabase connection and configuration validation
type HealthChecker struct {
	config *Config
	client *http.Client
}

// HealthCheckResult represents the result of a health check
type HealthCheckResult struct {
	Service      string                 `json:"service"`
	Status       string                 `json:"status"` // "healthy", "unhealthy", "degraded"
	Message      string                 `json:"message"`
	ResponseTime time.Duration          `json:"response_time"`
	Details      map[string]interface{} `json:"details,omitempty"`
	Timestamp    time.Time              `json:"timestamp"`
}

// SupabaseHealthResponse represents Supabase health API response
type SupabaseHealthResponse struct {
	Status      string `json:"status"`
	Version     string `json:"version,omitempty"`
	Description string `json:"description,omitempty"`
}

// NewHealthChecker creates a new health checker instance
func NewHealthChecker(config *Config) *HealthChecker {
	return &HealthChecker{
		config: config,
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// Add method to Config for creating health checker
func (c *Config) NewHealthChecker() *HealthChecker {
	return NewHealthChecker(c)
}

// CheckSupabaseHealth performs comprehensive health check for Supabase
// Task 1.3: Validate Supabase connection and API availability
func (h *HealthChecker) CheckSupabaseHealth(ctx context.Context) *HealthCheckResult {
	start := time.Now()
	result := &HealthCheckResult{
		Service:   "supabase",
		Status:    "unhealthy",
		Timestamp: start,
		Details:   make(map[string]interface{}),
	}

	log.Printf("🔍 Checking Supabase health...")

	// Check if configuration is valid
	if h.config.Supabase.URL == "" {
		result.Message = "Supabase URL not configured"
		result.ResponseTime = time.Since(start)
		return result
	}

	// Test Supabase REST API health endpoint
	healthURL := fmt.Sprintf("%s/rest/v1/", h.config.Supabase.URL)

	req, err := http.NewRequestWithContext(ctx, "GET", healthURL, nil)
	if err != nil {
		result.Message = fmt.Sprintf("Failed to create health check request: %v", err)
		result.ResponseTime = time.Since(start)
		return result
	}

	// Add required headers
	req.Header.Set("apikey", h.config.Supabase.AnonKey)
	req.Header.Set("Authorization", "Bearer "+h.config.Supabase.AnonKey)
	req.Header.Set("Content-Type", "application/json")

	// Perform the request
	resp, err := h.client.Do(req)
	if err != nil {
		result.Message = fmt.Sprintf("Failed to connect to Supabase: %v", err)
		result.ResponseTime = time.Since(start)
		return result
	}
	defer resp.Body.Close()

	result.ResponseTime = time.Since(start)
	result.Details["status_code"] = resp.StatusCode
	result.Details["response_time_ms"] = result.ResponseTime.Milliseconds()

	// Check response status
	if resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusNotFound {
		// 200 or 404 are both acceptable for REST API health check
		// 404 means the API is responding but no specific endpoint was found
		result.Status = "healthy"
		result.Message = "Supabase REST API is accessible"
		result.Details["api_accessible"] = true

		log.Printf("✅ Supabase health check passed (status: %d, response time: %v)",
			resp.StatusCode, result.ResponseTime)
	} else if resp.StatusCode == http.StatusUnauthorized {
		result.Status = "degraded"
		result.Message = "Supabase API accessible but authentication failed"
		result.Details["api_accessible"] = true
		result.Details["auth_issue"] = true

		log.Printf("⚠️ Supabase health check degraded: authentication issue (status: %d)", resp.StatusCode)
	} else {
		result.Status = "unhealthy"
		result.Message = fmt.Sprintf("Supabase API returned unexpected status: %d", resp.StatusCode)
		result.Details["api_accessible"] = false

		log.Printf("❌ Supabase health check failed (status: %d, response time: %v)",
			resp.StatusCode, result.ResponseTime)
	}

	return result
}

// CheckDatabaseHealth performs database connectivity check
// Task 1.3: Validate database connection through Supabase
func (h *HealthChecker) CheckDatabaseHealth(ctx context.Context) *HealthCheckResult {
	start := time.Now()
	result := &HealthCheckResult{
		Service:   "database",
		Status:    "unhealthy",
		Timestamp: start,
		Details:   make(map[string]interface{}),
	}

	log.Printf("🔍 Checking database health through Supabase...")

	// Simple query to test database connectivity
	queryURL := fmt.Sprintf("%s/rest/v1/rpc/version", h.config.Supabase.URL)

	req, err := http.NewRequestWithContext(ctx, "POST", queryURL, nil)
	if err != nil {
		result.Message = fmt.Sprintf("Failed to create database health check request: %v", err)
		result.ResponseTime = time.Since(start)
		return result
	}

	// Add required headers
	req.Header.Set("apikey", h.config.Supabase.AnonKey)
	req.Header.Set("Authorization", "Bearer "+h.config.Supabase.AnonKey)
	req.Header.Set("Content-Type", "application/json")

	// Perform the request
	resp, err := h.client.Do(req)
	if err != nil {
		result.Message = fmt.Sprintf("Failed to connect to database: %v", err)
		result.ResponseTime = time.Since(start)
		return result
	}
	defer resp.Body.Close()

	result.ResponseTime = time.Since(start)
	result.Details["status_code"] = resp.StatusCode
	result.Details["response_time_ms"] = result.ResponseTime.Milliseconds()

	// Check response status
	if resp.StatusCode == http.StatusOK {
		result.Status = "healthy"
		result.Message = "Database is accessible through Supabase"
		result.Details["database_accessible"] = true

		log.Printf("✅ Database health check passed (response time: %v)", result.ResponseTime)
	} else if resp.StatusCode == http.StatusNotFound {
		// RPC function might not exist, but database is accessible
		result.Status = "healthy"
		result.Message = "Database is accessible (RPC function not found)"
		result.Details["database_accessible"] = true
		result.Details["rpc_function_missing"] = true

		log.Printf("✅ Database health check passed with warning (RPC not found, response time: %v)",
			result.ResponseTime)
	} else {
		result.Status = "unhealthy"
		result.Message = fmt.Sprintf("Database health check failed with status: %d", resp.StatusCode)
		result.Details["database_accessible"] = false

		log.Printf("❌ Database health check failed (status: %d, response time: %v)",
			resp.StatusCode, result.ResponseTime)
	}

	return result
}

// CheckAllServices performs health checks for all configured services
// Task 1.3: Comprehensive health check for all external dependencies
func (h *HealthChecker) CheckAllServices(ctx context.Context) map[string]*HealthCheckResult {
	log.Printf("🔍 Starting comprehensive health checks...")

	results := make(map[string]*HealthCheckResult)

	// Check Supabase health
	results["supabase"] = h.CheckSupabaseHealth(ctx)

	// Check database health
	results["database"] = h.CheckDatabaseHealth(ctx)

	// Log overall health status
	healthyCount := 0
	totalCount := len(results)

	for service, result := range results {
		if result.Status == "healthy" {
			healthyCount++
		}
		log.Printf("📊 %s: %s (%v)", service, result.Status, result.ResponseTime)
	}

	log.Printf("🎯 Health check summary: %d/%d services healthy", healthyCount, totalCount)

	return results
}

// ValidateEnvironmentVariables checks that all required environment variables are set
// Task 1.3: Environment variable validation for different environments
func (h *HealthChecker) ValidateEnvironmentVariables() error {
	log.Printf("🔍 Validating environment variables...")

	requiredVars := map[string]string{
		"CARNOW_SUPABASE_URL":        "Supabase project URL",
		"CARNOW_SUPABASE_ANON_KEY":   "Supabase anonymous key",
		"CARNOW_SUPABASE_JWT_SECRET": "Supabase JWT secret",
	}

	// Additional required variables for production
	if h.config.App.Environment == "production" {
		requiredVars["CARNOW_SUPABASE_SERVICE_ROLE_KEY"] = "Supabase service role key"
		requiredVars["CARNOW_JWT_SECRET"] = "JWT secret for token generation"
		requiredVars["CARNOW_SECURITY_ENCRYPTION_KEY"] = "Security encryption key"
	}

	var missingVars []string
	for envVar, description := range requiredVars {
		if value := h.getEnvValue(envVar); value == "" {
			missingVars = append(missingVars, fmt.Sprintf("%s (%s)", envVar, description))
		} else {
			// Check for mock data patterns (Forever Plan compliance)
			if h.containsMockData(value) {
				return fmt.Errorf("mock data detected in environment variable %s", envVar)
			}
		}
	}

	if len(missingVars) > 0 {
		return fmt.Errorf("missing required environment variables:\n%s",
			fmt.Sprintf("- %s", missingVars))
	}

	log.Printf("✅ All required environment variables are set")
	return nil
}

// getEnvValue gets environment variable value based on config
func (h *HealthChecker) getEnvValue(envVar string) string {
	switch envVar {
	case "CARNOW_SUPABASE_URL":
		return h.config.Supabase.URL
	case "CARNOW_SUPABASE_ANON_KEY":
		return h.config.Supabase.AnonKey
	case "CARNOW_SUPABASE_JWT_SECRET":
		return h.config.Supabase.JWTSecret
	case "CARNOW_SUPABASE_SERVICE_ROLE_KEY":
		return h.config.Supabase.ServiceRoleKey
	case "CARNOW_JWT_SECRET":
		return h.config.JWT.Secret
	case "CARNOW_SECURITY_ENCRYPTION_KEY":
		return h.config.Security.EncryptionKey
	default:
		return ""
	}
}

// containsMockData checks if a value contains mock data patterns
func (h *HealthChecker) containsMockData(value string) bool {
	// Skip checking very long values (likely secrets/tokens)
	if len(value) > 100 {
		return false
	}

	// Skip empty values
	if strings.TrimSpace(value) == "" {
		return false
	}

	mockPatterns := []string{"mock_", "test_", "fake_", "sample_", "demo_", "example"}
	lowerValue := strings.ToLower(value)

	for _, pattern := range mockPatterns {
		if strings.Contains(lowerValue, pattern) {
			return true
		}
	}
	return false
}
