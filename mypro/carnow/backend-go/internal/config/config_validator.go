package config

import (
	"fmt"
	"log"
	"net/url"
	"os"
	"regexp"
	"strings"
	"time"
)

// ConfigValidator provides comprehensive configuration validation
type ConfigValidator struct {
	cfg *Config
}

// NewConfigValidator creates a new configuration validator
func NewConfigValidator(cfg *Config) *ConfigValidator {
	return &ConfigValidator{cfg: cfg}
}

// ValidateAll performs comprehensive configuration validation
func (v *ConfigValidator) ValidateAll() error {
	log.Printf("🔍 Starting comprehensive configuration validation...")

	validations := []struct {
		name string
		fn   func() error
	}{
		{"Environment", v.validateEnvironment},
		{"Server", v.validateServer},
		{"Database", v.validateDatabase},
		{"Authentication", v.validateAuthentication},
		{"Security", v.validateSecurity},
		{"Logging", v.validateLogging},
		{"Features", v.validateFeatures},
	}

	for _, validation := range validations {
		log.Printf("🔍 Validating %s configuration...", validation.name)
		if err := validation.fn(); err != nil {
			return fmt.Errorf("%s validation failed: %w", validation.name, err)
		}
		log.Printf("✅ %s configuration is valid", validation.name)
	}

	log.Printf("✅ All configuration validation passed!")
	return nil
}

// validateEnvironment validates environment-specific settings
func (v *ConfigValidator) validateEnvironment() error {
	// Validate environment
	validEnvs := []string{"development", "staging", "production"}
	if !contains(validEnvs, v.cfg.App.Environment) {
		return fmt.Errorf("invalid environment '%s', must be one of: %s",
			v.cfg.App.Environment, strings.Join(validEnvs, ", "))
	}

	// Validate app name
	if v.cfg.App.Name == "" {
		return fmt.Errorf("app name is required")
	}

	// Validate timezone
	if v.cfg.App.Timezone != "" {
		if _, err := time.LoadLocation(v.cfg.App.Timezone); err != nil {
			return fmt.Errorf("invalid timezone '%s': %w", v.cfg.App.Timezone, err)
		}
	}

	return nil
}

// validateServer validates server configuration
func (v *ConfigValidator) validateServer() error {
	// Validate port
	if v.cfg.Server.Port < 1 || v.cfg.Server.Port > 65535 {
		return fmt.Errorf("invalid server port %d, must be between 1 and 65535", v.cfg.Server.Port)
	}

	// Validate timeouts
	if v.cfg.Server.ReadTimeout < time.Second {
		return fmt.Errorf("read timeout too short, minimum 1 second")
	}

	if v.cfg.Server.WriteTimeout < time.Second {
		return fmt.Errorf("write timeout too short, minimum 1 second")
	}

	if v.cfg.Server.IdleTimeout < time.Second {
		return fmt.Errorf("idle timeout too short, minimum 1 second")
	}

	// Validate max header bytes
	if v.cfg.Server.MaxHeaderBytes < 1024 {
		return fmt.Errorf("max header bytes too small, minimum 1024 bytes")
	}

	return nil
}

// validateDatabase validates database configuration
func (v *ConfigValidator) validateDatabase() error {
	// Validate host
	if v.cfg.Database.Host == "" {
		return fmt.Errorf("database host is required")
	}

	// Validate port
	if v.cfg.Database.Port < 1 || v.cfg.Database.Port > 65535 {
		return fmt.Errorf("invalid database port %d", v.cfg.Database.Port)
	}

	// Validate connection pool settings
	if v.cfg.Database.MaxOpenConns < 1 {
		return fmt.Errorf("max open connections must be at least 1")
	}

	if v.cfg.Database.MaxIdleConns < 0 {
		return fmt.Errorf("max idle connections cannot be negative")
	}

	if v.cfg.Database.MaxIdleConns > v.cfg.Database.MaxOpenConns {
		return fmt.Errorf("max idle connections cannot exceed max open connections")
	}

	// Validate SSL mode
	validSSLModes := []string{"disable", "require", "verify-ca", "verify-full"}
	if !contains(validSSLModes, v.cfg.Database.SSLMode) {
		return fmt.Errorf("invalid SSL mode '%s', must be one of: %s",
			v.cfg.Database.SSLMode, strings.Join(validSSLModes, ", "))
	}

	return nil
}

// validateAuthentication validates authentication configuration
func (v *ConfigValidator) validateAuthentication() error {
	// Validate Supabase configuration
	if err := v.validateSupabaseConfig(); err != nil {
		return fmt.Errorf("Supabase config: %w", err)
	}

	// Validate JWT configuration
	if err := v.validateJWTConfig(); err != nil {
		return fmt.Errorf("JWT config: %w", err)
	}

	// Validate Google OAuth configuration
	if err := v.validateGoogleConfig(); err != nil {
		return fmt.Errorf("Google OAuth config: %w", err)
	}

	return nil
}

// validateSupabaseConfig validates Supabase-specific configuration
// Task 1.3: Enhanced Supabase configuration validation with health checks
func (v *ConfigValidator) validateSupabaseConfig() error {
	log.Printf("🔍 Validating Supabase configuration...")

	// Validate URL
	if v.cfg.Supabase.URL == "" {
		return fmt.Errorf("Supabase URL is required")
	}

	// Validate URL format and structure
	parsedURL, err := url.Parse(v.cfg.Supabase.URL)
	if err != nil {
		return fmt.Errorf("invalid Supabase URL format: %w", err)
	}

	// Validate HTTPS requirement
	if !strings.HasPrefix(v.cfg.Supabase.URL, "https://") {
		if v.cfg.App.Environment == "production" {
			return fmt.Errorf("Supabase URL must use HTTPS in production")
		}
		log.Printf("⚠️ WARNING: Supabase URL not using HTTPS in %s environment", v.cfg.App.Environment)
	}

	// Validate Supabase URL pattern
	if !strings.Contains(parsedURL.Host, ".supabase.co") {
		return fmt.Errorf("invalid Supabase URL: must be a valid Supabase domain (*.supabase.co)")
	}

	// Validate project reference
	if v.cfg.Supabase.ProjectRef == "" {
		return fmt.Errorf("Supabase project reference is required")
	}

	// Validate project reference format (should be alphanumeric, 20 characters)
	if len(v.cfg.Supabase.ProjectRef) != 20 {
		return fmt.Errorf("invalid Supabase project reference: must be 20 characters long")
	}

	// Validate keys
	if v.cfg.Supabase.AnonKey == "" {
		return fmt.Errorf("Supabase anon key is required")
	}

	// Validate anon key format (should start with 'eyJ' for JWT)
	if len(v.cfg.Supabase.AnonKey) < 50 {
		return fmt.Errorf("Supabase anon key appears to be invalid (too short)")
	}

	if !strings.HasPrefix(v.cfg.Supabase.AnonKey, "eyJ") {
		return fmt.Errorf("invalid Supabase anonymous key format: should be a valid JWT token")
	}

	// Validate service role key
	if v.cfg.Supabase.ServiceRoleKey == "" {
		return fmt.Errorf("Supabase service role key is required")
	}

	if !strings.HasPrefix(v.cfg.Supabase.ServiceRoleKey, "eyJ") {
		return fmt.Errorf("invalid Supabase service role key format: should be a valid JWT token")
	}

	if v.cfg.Supabase.JWTSecret == "" {
		return fmt.Errorf("Supabase JWT secret is required")
	}

	if len(v.cfg.Supabase.JWTSecret) < 32 {
		return fmt.Errorf("Supabase JWT secret appears to be invalid (too short)")
	}

	// Check for mock data patterns (Forever Plan compliance)
	mockPatterns := []string{"mock_", "test_", "fake_", "sample_", "demo_", "example"}
	for _, pattern := range mockPatterns {
		if strings.Contains(strings.ToLower(v.cfg.Supabase.URL), pattern) {
			return fmt.Errorf("mock data detected in Supabase URL: %s", v.cfg.Supabase.URL)
		}
		if strings.Contains(strings.ToLower(v.cfg.Supabase.ProjectRef), pattern) {
			return fmt.Errorf("mock data detected in Supabase project reference: %s", v.cfg.Supabase.ProjectRef)
		}
	}

	log.Printf("✅ Supabase configuration validation passed")
	return nil
}

// validateJWTConfig validates JWT configuration
func (v *ConfigValidator) validateJWTConfig() error {
	// Validate secret
	if v.cfg.JWT.Secret == "" {
		return fmt.Errorf("JWT secret is required")
	}

	if len(v.cfg.JWT.Secret) < 32 {
		return fmt.Errorf("JWT secret must be at least 32 characters for security")
	}

	// Validate algorithm
	validAlgorithms := []string{"HS256", "HS384", "HS512", "RS256", "RS384", "RS512"}
	if !contains(validAlgorithms, v.cfg.JWT.Algorithm) {
		return fmt.Errorf("invalid JWT algorithm '%s', must be one of: %s",
			v.cfg.JWT.Algorithm, strings.Join(validAlgorithms, ", "))
	}

	// Validate expiration times
	if v.cfg.JWT.ExpiresIn < 5*time.Minute {
		return fmt.Errorf("JWT expires_in too short, minimum 5 minutes")
	}

	if v.cfg.JWT.ExpiresIn > 24*time.Hour {
		return fmt.Errorf("JWT expires_in too long, maximum 24 hours")
	}

	if v.cfg.JWT.RefreshExpiresIn < 24*time.Hour {
		return fmt.Errorf("JWT refresh expires_in too short, minimum 24 hours")
	}

	if v.cfg.JWT.RefreshExpiresIn > 720*time.Hour { // 30 days
		return fmt.Errorf("JWT refresh expires_in too long, maximum 30 days")
	}

	// Validate issuer and audience
	if v.cfg.JWT.Issuer == "" {
		return fmt.Errorf("JWT issuer is required")
	}

	if v.cfg.JWT.Audience == "" {
		return fmt.Errorf("JWT audience is required")
	}

	return nil
}

// validateGoogleConfig validates Google OAuth configuration
func (v *ConfigValidator) validateGoogleConfig() error {
	// Google OAuth is optional, but if client ID is set, validate the configuration
	if v.cfg.Google.ClientID != "" {
		if v.cfg.Google.ClientSecret == "" {
			return fmt.Errorf("Google OAuth client secret is required when client ID is set")
		}

		// Validate client ID format (should end with .apps.googleusercontent.com)
		if !strings.HasSuffix(v.cfg.Google.ClientID, ".apps.googleusercontent.com") {
			return fmt.Errorf("Google OAuth client ID format appears invalid")
		}

		if len(v.cfg.Google.ClientSecret) < 20 {
			return fmt.Errorf("Google OAuth client secret appears to be invalid (too short)")
		}

		log.Printf("✅ Google OAuth configuration is valid and enabled")
	} else {
		log.Printf("ℹ️ Google OAuth is disabled (no client ID configured)")
	}

	return nil
}

// validateSecurity validates security configuration
func (v *ConfigValidator) validateSecurity() error {
	// Validate encryption key
	if v.cfg.Security.EncryptionKey == "" {
		if v.cfg.App.Environment == "production" {
			return fmt.Errorf("encryption key is required in production")
		}
		log.Printf("⚠️ WARNING: No encryption key set in %s environment", v.cfg.App.Environment)
	} else if len(v.cfg.Security.EncryptionKey) < 32 {
		return fmt.Errorf("encryption key must be at least 32 characters")
	}

	// Validate rate limiting
	if v.cfg.Security.RateLimit.LoginAttempts < 1 {
		return fmt.Errorf("login rate limit attempts must be at least 1")
	}

	if v.cfg.Security.RateLimit.RegisterAttempts < 1 {
		return fmt.Errorf("register rate limit attempts must be at least 1")
	}

	if v.cfg.Security.RateLimit.WindowMinutes < 1 {
		return fmt.Errorf("rate limit window must be at least 1 minute")
	}

	// Validate CORS
	if len(v.cfg.Security.CORS.AllowedOrigins) == 0 {
		if v.cfg.App.Environment == "production" {
			log.Printf("⚠️ WARNING: No CORS origins configured in production")
		}
	}

	return nil
}

// validateLogging validates logging configuration
func (v *ConfigValidator) validateLogging() error {
	// Validate log level
	validLevels := []string{"debug", "info", "warn", "error", "fatal"}
	if !contains(validLevels, v.cfg.Logging.Level) {
		return fmt.Errorf("invalid log level '%s', must be one of: %s",
			v.cfg.Logging.Level, strings.Join(validLevels, ", "))
	}

	// Validate log format
	validFormats := []string{"json", "text"}
	if !contains(validFormats, v.cfg.Logging.Format) {
		return fmt.Errorf("invalid log format '%s', must be one of: %s",
			v.cfg.Logging.Format, strings.Join(validFormats, ", "))
	}

	return nil
}

// validateFeatures validates feature flags (Forever Plan compliance)
func (v *ConfigValidator) validateFeatures() error {
	// Forever Plan: Ensure prohibited features are disabled
	prohibitedFeatures := map[string]bool{
		"EnableComplexAuth":      v.cfg.Features.EnableComplexAuth,
		"EnableDualDatabase":     v.cfg.Features.EnableDualDatabase,
		"EnableSyncServices":     v.cfg.Features.EnableSyncServices,
		"EnableEnhancedFeatures": v.cfg.Features.EnableEnhancedFeatures,
	}

	var enabledProhibited []string
	for feature, enabled := range prohibitedFeatures {
		if enabled {
			enabledProhibited = append(enabledProhibited, feature)
		}
	}

	if len(enabledProhibited) > 0 {
		log.Printf("⚠️ WARNING: Forever Plan violation - prohibited features enabled: %s",
			strings.Join(enabledProhibited, ", "))
		log.Printf("ℹ️ Forever Plan requires: Flutter (UI Only) → Go API → Supabase (Data Only)")
	}

	return nil
}

// ValidateEnvironmentVariables validates that all required environment variables are set
func ValidateEnvironmentVariables() error {
	log.Printf("🌍 Validating environment variables...")

	// For local development, we can use config file values
	// For production deployment, environment variables should be set
	environment := os.Getenv("CARNOW_APP_ENVIRONMENT")
	if environment == "" {
		environment = "development"
	}

	// In development, we can skip environment variable validation
	// since values are loaded from config file
	if environment == "development" {
		log.Printf("✅ Environment variables validation skipped for development (using config file)")
		return nil
	}

	requiredVars := map[string]string{
		"CARNOW_SUPABASE_URL":      "Supabase project URL",
		"CARNOW_SUPABASE_ANON_KEY": "Supabase anonymous key",
	}

	productionVars := map[string]string{
		"CARNOW_SUPABASE_JWT_SECRET":     "Supabase JWT secret",
		"CARNOW_JWT_SECRET":              "JWT signing secret",
		"CARNOW_SECURITY_ENCRYPTION_KEY": "Security encryption key",
	}

	// Check required variables for all environments
	var missing []string
	for envVar, description := range requiredVars {
		if os.Getenv(envVar) == "" {
			missing = append(missing, fmt.Sprintf("%s (%s)", envVar, description))
		}
	}

	// Check production-specific variables
	if environment == "production" {
		for envVar, description := range productionVars {
			if os.Getenv(envVar) == "" {
				missing = append(missing, fmt.Sprintf("%s (%s)", envVar, description))
			}
		}
	}

	if len(missing) > 0 {
		return fmt.Errorf("missing required environment variables:\n%s", strings.Join(missing, "\n"))
	}

	log.Printf("✅ All required environment variables are set")
	return nil
}

// contains checks if a slice contains a string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// ValidateSecrets validates that secrets meet security requirements
func ValidateSecrets(cfg *Config) error {
	log.Printf("🔐 Validating secrets security...")

	// Check for common weak patterns
	weakPatterns := []*regexp.Regexp{
		regexp.MustCompile(`^(password|secret|key|token)$`),
		regexp.MustCompile(`^(123|abc|test|demo|default)`),
	}

	secrets := map[string]string{
		"JWT Secret":              cfg.JWT.Secret,
		"Security Encryption Key": cfg.Security.EncryptionKey,
		"Supabase JWT Secret":     cfg.Supabase.JWTSecret,
	}

	for name, secret := range secrets {
		if secret == "" {
			continue // Already validated in other functions
		}

		for _, pattern := range weakPatterns {
			if pattern.MatchString(strings.ToLower(secret)) {
				return fmt.Errorf("%s appears to be weak or default value", name)
			}
		}

		// Check entropy (basic check)
		if len(secret) > 0 && isLowEntropy(secret) {
			log.Printf("⚠️ WARNING: %s may have low entropy", name)
		}
	}

	log.Printf("✅ Secrets validation passed")
	return nil
}

// isLowEntropy performs a basic entropy check
func isLowEntropy(s string) bool {
	if len(s) < 16 {
		return true
	}

	// Count unique characters
	unique := make(map[rune]bool)
	for _, r := range s {
		unique[r] = true
	}

	// If less than 50% unique characters, consider low entropy
	return float64(len(unique))/float64(len(s)) < 0.5
}
