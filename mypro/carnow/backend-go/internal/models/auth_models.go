package models

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"carnow-backend/internal/shared/errors"
)

// AuthResponse represents the complete authentication response from Supabase
// Task 2.2: Complete auth response model with proper JSON serialization
type AuthResponse struct {
	AccessToken  string    `json:"access_token" validate:"required"`
	RefreshToken string    `json:"refresh_token" validate:"required"`
	ExpiresIn    int       `json:"expires_in" validate:"required,min=1"`
	TokenType    string    `json:"token_type" validate:"required"`
	User         *User     `json:"user" validate:"required"`
	ExpiresAt    time.Time `json:"expires_at"`
	CreatedAt    time.Time `json:"created_at"`
}

// User represents user data from Supabase with complete field mapping
// Task 2.2: Complete user model with Supabase fields and validation
type User struct {
	ID                 string                 `json:"id" validate:"required,uuid"`
	Email              string                 `json:"email" validate:"required,email"`
	EmailConfirmed     bool                   `json:"email_confirmed_at,omitempty"`
	EmailConfirmedAt   *time.Time             `json:"email_confirmed_at,omitempty"`
	Phone              string                 `json:"phone,omitempty" validate:"omitempty,phone"`
	PhoneConfirmed     bool                   `json:"phone_confirmed,omitempty"`
	PhoneConfirmedAt   *time.Time             `json:"phone_confirmed_at,omitempty"`
	CreatedAt          time.Time              `json:"created_at" validate:"required"`
	UpdatedAt          time.Time              `json:"updated_at" validate:"required"`
	LastSignInAt       *time.Time             `json:"last_sign_in_at,omitempty"`
	Role               string                 `json:"role,omitempty" validate:"omitempty,oneof=authenticated anon service_role"`
	Aud                string                 `json:"aud,omitempty"`
	ConfirmationSentAt *time.Time             `json:"confirmation_sent_at,omitempty"`
	RecoverySentAt     *time.Time             `json:"recovery_sent_at,omitempty"`
	EmailChangeSentAt  *time.Time             `json:"email_change_sent_at,omitempty"`
	NewEmail           string                 `json:"new_email,omitempty" validate:"omitempty,email"`
	InvitedAt          *time.Time             `json:"invited_at,omitempty"`
	ActionLink         string                 `json:"action_link,omitempty"`
	UserMetadata       map[string]interface{} `json:"user_metadata,omitempty"`
	AppMetadata        map[string]interface{} `json:"app_metadata,omitempty"`
	Identities         []UserIdentity         `json:"identities,omitempty"`
}

// UserIdentity represents user identity information from Supabase
// Task 2.2: User identity model for OAuth providers
type UserIdentity struct {
	ID           string                 `json:"id" validate:"required"`
	UserID       string                 `json:"user_id" validate:"required,uuid"`
	Provider     string                 `json:"provider" validate:"required"`
	IdentityData map[string]interface{} `json:"identity_data,omitempty"`
	CreatedAt    time.Time              `json:"created_at" validate:"required"`
	UpdatedAt    time.Time              `json:"updated_at" validate:"required"`
	LastSignInAt *time.Time             `json:"last_sign_in_at,omitempty"`
}

// SupabaseConfig represents Supabase configuration settings
// Task 2.2: Configuration model with validation
type SupabaseConfig struct {
	URL              string        `json:"url" validate:"required,url"`
	AnonKey          string        `json:"anon_key" validate:"required,min=32"`
	ServiceRoleKey   string        `json:"service_role_key,omitempty" validate:"omitempty,min=32"`
	JWTSecret        string        `json:"jwt_secret,omitempty" validate:"omitempty,min=32"`
	ProjectRef       string        `json:"project_ref" validate:"required,alphanum,min=20,max=20"`
	Region           string        `json:"region,omitempty" validate:"omitempty,alpha"`
	Timeout          time.Duration `json:"timeout" validate:"required,min=1s,max=60s"`
	MaxRetries       int           `json:"max_retries" validate:"required,min=1,max=10"`
	RetryDelay       time.Duration `json:"retry_delay" validate:"required,min=100ms,max=10s"`
	EnableLogging    bool          `json:"enable_logging"`
	EnableMetrics    bool          `json:"enable_metrics"`
	RateLimitEnabled bool          `json:"rate_limit_enabled"`
	RateLimitRPS     int           `json:"rate_limit_rps" validate:"omitempty,min=1,max=1000"`
}

// AuthRequest represents authentication request data
// Task 2.2: Auth request model with validation
type AuthRequest struct {
	Email    string                 `json:"email" validate:"required,email"`
	Password string                 `json:"password" validate:"required,min=6,max=128"`
	Provider string                 `json:"provider,omitempty" validate:"omitempty,oneof=google github apple"`
	Data     map[string]interface{} `json:"data,omitempty"`
}

// RefreshTokenRequest represents refresh token request
// Task 2.2: Refresh token request model
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required,min=32"`
}

// SignOutRequest represents sign out request
// Task 2.2: Sign out request model
type SignOutRequest struct {
	AccessToken string `json:"access_token,omitempty"`
	Scope       string `json:"scope,omitempty" validate:"omitempty,oneof=global local"`
}

// UserUpdateRequest represents user update request
// Task 2.2: User update request model
type UserUpdateRequest struct {
	Email        string                 `json:"email,omitempty" validate:"omitempty,email"`
	Password     string                 `json:"password,omitempty" validate:"omitempty,min=6,max=128"`
	Phone        string                 `json:"phone,omitempty" validate:"omitempty,phone"`
	Data         map[string]interface{} `json:"data,omitempty"`
	UserMetadata map[string]interface{} `json:"user_metadata,omitempty"`
	AppMetadata  map[string]interface{} `json:"app_metadata,omitempty"`
}

// PasswordResetRequest represents password reset request
// Task 2.2: Password reset request model
type PasswordResetRequest struct {
	Email        string `json:"email" validate:"required,email"`
	RedirectTo   string `json:"redirect_to,omitempty" validate:"omitempty,url"`
	CaptchaToken string `json:"captcha_token,omitempty"`
}

// EmailVerificationRequest represents email verification request
// Task 2.2: Email verification request model
type EmailVerificationRequest struct {
	Email        string `json:"email" validate:"required,email"`
	RedirectTo   string `json:"redirect_to,omitempty" validate:"omitempty,url"`
	CaptchaToken string `json:"captcha_token,omitempty"`
}

// AuthErrorResponse represents authentication error response
// Task 2.2: Error response model for API consistency
type AuthErrorResponse struct {
	Error            string                 `json:"error"`
	ErrorDescription string                 `json:"error_description,omitempty"`
	ErrorCode        string                 `json:"error_code,omitempty"`
	Message          string                 `json:"message,omitempty"`
	Details          map[string]interface{} `json:"details,omitempty"`
	Timestamp        time.Time              `json:"timestamp"`
	RequestID        string                 `json:"request_id,omitempty"`
	Path             string                 `json:"path,omitempty"`
	Method           string                 `json:"method,omitempty"`
}

// Validate validates the AuthResponse model
// Task 2.2: Validation methods for data integrity
func (ar *AuthResponse) Validate() error {
	if ar == nil {
		return errors.ErrInternalError("auth response is nil")
	}

	if ar.AccessToken == "" {
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "access token is required")
	}

	if ar.RefreshToken == "" {
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "refresh token is required")
	}

	if ar.ExpiresIn <= 0 {
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "expires_in must be positive")
	}

	if ar.TokenType == "" {
		ar.TokenType = "Bearer" // Default value
	}

	if ar.User == nil {
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "user data is required")
	}

	// Validate user data
	if err := ar.User.Validate(); err != nil {
		return err
	}

	// Check for mock data (Forever Plan compliance)
	if containsMockData(ar.AccessToken) {
		return errors.ErrMockDataDetected("access token")
	}

	if containsMockData(ar.RefreshToken) {
		return errors.ErrMockDataDetected("refresh token")
	}

	return nil
}

// Validate validates the User model
// Task 2.2: User validation with Forever Plan compliance
func (u *User) Validate() error {
	if u == nil {
		return errors.ErrInternalError("user is nil")
	}

	if u.ID == "" {
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "user ID is required")
	}

	if u.Email == "" {
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "user email is required")
	}

	// Validate email format
	if !isValidEmail(u.Email) {
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "invalid email format")
	}

	// Validate UUID format for ID
	if !isValidUUID(u.ID) {
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "invalid user ID format")
	}

	// Check for mock data (Forever Plan compliance)
	mockFields := []struct {
		field string
		value string
	}{
		{"user_id", u.ID},
		{"email", u.Email},
		{"role", u.Role},
		{"phone", u.Phone},
	}

	for _, field := range mockFields {
		if containsMockData(field.value) {
			return errors.ErrMockDataDetected(fmt.Sprintf("user %s", field.field))
		}
	}

	// Validate phone number if provided
	if u.Phone != "" && !isValidPhone(u.Phone) {
		return errors.NewAuthError(errors.ErrCodeValidationFailed, "invalid phone number format")
	}

	// Validate role if provided
	if u.Role != "" {
		validRoles := []string{"authenticated", "anon", "service_role"}
		if !contains(validRoles, u.Role) {
			return errors.NewAuthError(errors.ErrCodeValidationFailed, "invalid user role")
		}
	}

	return nil
}

// Validate validates the SupabaseConfig model
// Task 2.2: Configuration validation with security checks
func (sc *SupabaseConfig) Validate() error {
	if sc == nil {
		return errors.ErrInternalError("supabase config is nil")
	}

	if sc.URL == "" {
		return errors.NewAuthError(errors.ErrCodeConfigurationError, "Supabase URL is required")
	}

	if sc.AnonKey == "" {
		return errors.NewAuthError(errors.ErrCodeConfigurationError, "Supabase anon key is required")
	}

	if sc.ProjectRef == "" {
		return errors.NewAuthError(errors.ErrCodeConfigurationError, "Supabase project ref is required")
	}

	// Validate URL format
	if !isValidURL(sc.URL) {
		return errors.NewAuthError(errors.ErrCodeConfigurationError, "invalid Supabase URL format")
	}

	// Validate project ref format (20 character alphanumeric)
	if !isValidProjectRef(sc.ProjectRef) {
		return errors.NewAuthError(errors.ErrCodeConfigurationError, "invalid project ref format")
	}

	// Check for mock data (Forever Plan compliance)
	mockFields := []struct {
		field string
		value string
	}{
		{"url", sc.URL},
		{"anon_key", sc.AnonKey},
		{"service_role_key", sc.ServiceRoleKey},
		{"project_ref", sc.ProjectRef},
	}

	for _, field := range mockFields {
		if containsMockData(field.value) {
			return errors.ErrMockDataDetected(fmt.Sprintf("config %s", field.field))
		}
	}

	// Set defaults
	if sc.Timeout == 0 {
		sc.Timeout = 30 * time.Second
	}

	if sc.MaxRetries == 0 {
		sc.MaxRetries = 3
	}

	if sc.RetryDelay == 0 {
		sc.RetryDelay = 1 * time.Second
	}

	if sc.RateLimitRPS == 0 {
		sc.RateLimitRPS = 100
	}

	return nil
}

// ToJSON converts the model to JSON string
// Task 2.2: JSON serialization helper
func (ar *AuthResponse) ToJSON() (string, error) {
	data, err := json.Marshal(ar)
	if err != nil {
		return "", errors.ErrInternalError("failed to marshal auth response")
	}
	return string(data), nil
}

// ToJSON converts the user model to JSON string
// Task 2.2: JSON serialization helper
func (u *User) ToJSON() (string, error) {
	data, err := json.Marshal(u)
	if err != nil {
		return "", errors.ErrInternalError("failed to marshal user")
	}
	return string(data), nil
}

// FromJSON creates AuthResponse from JSON string
// Task 2.2: JSON deserialization helper
func (ar *AuthResponse) FromJSON(jsonStr string) error {
	if err := json.Unmarshal([]byte(jsonStr), ar); err != nil {
		return errors.ErrInternalError("failed to unmarshal auth response")
	}
	return ar.Validate()
}

// FromJSON creates User from JSON string
// Task 2.2: JSON deserialization helper
func (u *User) FromJSON(jsonStr string) error {
	if err := json.Unmarshal([]byte(jsonStr), u); err != nil {
		return errors.ErrInternalError("failed to unmarshal user")
	}
	return u.Validate()
}

// Helper functions for validation
// Task 2.2: Validation helper functions

// isValidEmail validates email format
func isValidEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// isValidUUID validates UUID format
func isValidUUID(uuid string) bool {
	uuidRegex := regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`)
	return uuidRegex.MatchString(uuid)
}

// isValidPhone validates phone number format
func isValidPhone(phone string) bool {
	// Basic phone validation - can be enhanced based on requirements
	phoneRegex := regexp.MustCompile(`^\+?[1-9]\d{1,14}$`)
	return phoneRegex.MatchString(phone)
}

// isValidURL validates URL format
func isValidURL(url string) bool {
	urlRegex := regexp.MustCompile(`^https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(/.*)?$`)
	return urlRegex.MatchString(url)
}

// isValidProjectRef validates Supabase project ref format
func isValidProjectRef(ref string) bool {
	// Supabase project refs are 20 character alphanumeric strings
	refRegex := regexp.MustCompile(`^[a-zA-Z0-9]{20}$`)
	return refRegex.MatchString(ref)
}

// contains checks if a slice contains a string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// containsMockData checks if a string contains mock data patterns
// Task 2.2: Forever Plan compliance - detect mock data
func containsMockData(value string) bool {
	if len(value) > 200 { // Skip very long values like JWT tokens
		return false
	}

	lowerValue := strings.ToLower(value)
	mockPatterns := []string{
		"mock", "test", "fake", "sample", "demo", "example",
		"localhost", "127.0.0.1", "0.0.0.0",
		"test@test", "fake@fake", "demo@demo",
		"placeholder", "dummy", "temp",
	}

	for _, pattern := range mockPatterns {
		if strings.Contains(lowerValue, pattern) {
			return true
		}
	}

	return false
}

// SanitizeForLogging removes sensitive information for logging
// Task 2.2: Security helper for safe logging
func (ar *AuthResponse) SanitizeForLogging() map[string]interface{} {
	return map[string]interface{}{
		"token_type": ar.TokenType,
		"expires_in": ar.ExpiresIn,
		"expires_at": ar.ExpiresAt,
		"user_id":    ar.User.ID,
		"user_email": maskEmail(ar.User.Email),
		"created_at": ar.CreatedAt,
	}
}

// SanitizeForLogging removes sensitive information for logging
// Task 2.2: Security helper for safe logging
func (u *User) SanitizeForLogging() map[string]interface{} {
	return map[string]interface{}{
		"id":              u.ID,
		"email":           maskEmail(u.Email),
		"email_confirmed": u.EmailConfirmed,
		"phone":           maskPhone(u.Phone),
		"role":            u.Role,
		"created_at":      u.CreatedAt,
		"updated_at":      u.UpdatedAt,
		"last_sign_in_at": u.LastSignInAt,
	}
}

// maskEmail masks email for logging
func maskEmail(email string) string {
	if email == "" {
		return ""
	}

	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return "***@***"
	}

	username := parts[0]
	domain := parts[1]

	if len(username) <= 2 {
		return "***@" + domain
	}

	return username[:2] + "***@" + domain
}

// maskPhone masks phone number for logging
func maskPhone(phone string) string {
	if phone == "" {
		return ""
	}

	if len(phone) <= 4 {
		return "***"
	}

	return phone[:2] + "***" + phone[len(phone)-2:]
}

// GetUserDisplayName returns a safe display name for the user
// Task 2.2: Helper for user display
func (u *User) GetUserDisplayName() string {
	if u.Email != "" {
		return maskEmail(u.Email)
	}
	if u.Phone != "" {
		return maskPhone(u.Phone)
	}
	return "User " + u.ID[:8]
}

// IsExpired checks if the auth response is expired
// Task 2.2: Helper for token expiration check
func (ar *AuthResponse) IsExpired() bool {
	return time.Now().After(ar.ExpiresAt)
}

// TimeUntilExpiry returns the duration until token expiry
// Task 2.2: Helper for token expiration timing
func (ar *AuthResponse) TimeUntilExpiry() time.Duration {
	return time.Until(ar.ExpiresAt)
}

// IsActive checks if the user account is active
// Task 2.2: Helper for user status check
func (u *User) IsActive() bool {
	// User is active if email is confirmed and not disabled
	return u.EmailConfirmed && u.Role != "disabled"
}

// HasRole checks if the user has a specific role
// Task 2.2: Helper for role-based access control
func (u *User) HasRole(role string) bool {
	return u.Role == role
}

// GetMetadataValue safely gets a value from user metadata
// Task 2.2: Helper for metadata access
func (u *User) GetMetadataValue(key string) (interface{}, bool) {
	if u.UserMetadata == nil {
		return nil, false
	}
	value, exists := u.UserMetadata[key]
	return value, exists
}

// SetMetadataValue safely sets a value in user metadata
// Task 2.2: Helper for metadata modification
func (u *User) SetMetadataValue(key string, value interface{}) {
	if u.UserMetadata == nil {
		u.UserMetadata = make(map[string]interface{})
	}
	u.UserMetadata[key] = value
}
