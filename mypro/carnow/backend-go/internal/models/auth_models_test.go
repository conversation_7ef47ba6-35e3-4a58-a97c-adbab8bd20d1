package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestAuthResponse_Validate tests AuthResponse validation
// Task 2.3: Test auth response validation
func TestAuthResponse_Validate(t *testing.T) {
	tests := []struct {
		name        string
		authResp    *AuthResponse
		expectError bool
		errorType   string
	}{
		{
			name: "Valid AuthResponse",
			authResp: &AuthResponse{
				AccessToken:  "valid-access-token",
				RefreshToken: "valid-refresh-token",
				ExpiresIn:    3600,
				TokenType:    "Bearer",
				User: &User{
					ID:             "550e8400-e29b-41d4-a716-************",
					Email:          "<EMAIL>",
					EmailConfirmed: true,
					CreatedAt:      time.Now(),
					UpdatedAt:      time.Now(),
					Role:           "authenticated",
				},
				ExpiresAt: time.Now().Add(time.Hour),
			},
			expectError: false,
		},
		{
			name:        "<PERSON>l AuthResponse",
			authResp:    nil,
			expectError: true,
		},
		{
			name: "Missing Access Token",
			authResp: &AuthResponse{
				RefreshToken: "valid-refresh-token",
				ExpiresIn:    3600,
				TokenType:    "Bearer",
				User: &User{
					ID:             "550e8400-e29b-41d4-a716-************",
					Email:          "<EMAIL>",
					EmailConfirmed: true,
					CreatedAt:      time.Now(),
					UpdatedAt:      time.Now(),
					Role:           "authenticated",
				},
			},
			expectError: true,
		},
		{
			name: "Missing User",
			authResp: &AuthResponse{
				AccessToken:  "valid-access-token",
				RefreshToken: "valid-refresh-token",
				ExpiresIn:    3600,
				TokenType:    "Bearer",
				User:         nil,
			},
			expectError: true,
		},
		{
			name: "Mock Access Token",
			authResp: &AuthResponse{
				AccessToken:  "mock_access_token",
				RefreshToken: "valid-refresh-token",
				ExpiresIn:    3600,
				TokenType:    "Bearer",
				User: &User{
					ID:             "550e8400-e29b-41d4-a716-************",
					Email:          "<EMAIL>",
					EmailConfirmed: true,
					CreatedAt:      time.Now(),
					UpdatedAt:      time.Now(),
					Role:           "authenticated",
				},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.authResp.Validate()

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestUser_Validate tests User validation
// Task 2.3: Test user validation
func TestUser_Validate(t *testing.T) {
	tests := []struct {
		name        string
		user        *User
		expectError bool
	}{
		{
			name: "Valid User",
			user: &User{
				ID:             "550e8400-e29b-41d4-a716-************",
				Email:          "<EMAIL>",
				EmailConfirmed: true,
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
				Role:           "authenticated",
			},
			expectError: false,
		},
		{
			name:        "Nil User",
			user:        nil,
			expectError: true,
		},
		{
			name: "Missing ID",
			user: &User{
				Email:          "<EMAIL>",
				EmailConfirmed: true,
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
				Role:           "authenticated",
			},
			expectError: true,
		},
		{
			name: "Missing Email",
			user: &User{
				ID:             "550e8400-e29b-41d4-a716-************",
				EmailConfirmed: true,
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
				Role:           "authenticated",
			},
			expectError: true,
		},
		{
			name: "Invalid Email Format",
			user: &User{
				ID:             "550e8400-e29b-41d4-a716-************",
				Email:          "invalid-email",
				EmailConfirmed: true,
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
				Role:           "authenticated",
			},
			expectError: true,
		},
		{
			name: "Invalid UUID Format",
			user: &User{
				ID:             "invalid-uuid",
				Email:          "<EMAIL>",
				EmailConfirmed: true,
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
				Role:           "authenticated",
			},
			expectError: true,
		},
		{
			name: "Mock Email",
			user: &User{
				ID:             "550e8400-e29b-41d4-a716-************",
				Email:          "<EMAIL>",
				EmailConfirmed: true,
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
				Role:           "authenticated",
			},
			expectError: true,
		},
		{
			name: "Invalid Role",
			user: &User{
				ID:             "550e8400-e29b-41d4-a716-************",
				Email:          "<EMAIL>",
				EmailConfirmed: true,
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
				Role:           "invalid_role",
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.user.Validate()

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestSupabaseConfig_Validate tests SupabaseConfig validation
// Task 2.3: Test configuration validation
func TestSupabaseConfig_Validate(t *testing.T) {
	tests := []struct {
		name        string
		config      *SupabaseConfig
		expectError bool
	}{
		{
			name: "Valid Config",
			config: &SupabaseConfig{
				URL:        "https://project.supabase.co",
				AnonKey:    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb2plY3QiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MDk5NTIwMCwiZXhwIjoxOTU2MzcxMjAwfQ",
				ProjectRef: "abcdefghijklmnopqrst",
			},
			expectError: false,
		},
		{
			name:        "Nil Config",
			config:      nil,
			expectError: true,
		},
		{
			name: "Missing URL",
			config: &SupabaseConfig{
				AnonKey:    "valid-anon-key",
				ProjectRef: "abcdefghijklmnopqrst",
			},
			expectError: true,
		},
		{
			name: "Missing Anon Key",
			config: &SupabaseConfig{
				URL:        "https://project.supabase.co",
				ProjectRef: "abcdefghijklmnopqrst",
			},
			expectError: true,
		},
		{
			name: "Missing Project Ref",
			config: &SupabaseConfig{
				URL:     "https://project.supabase.co",
				AnonKey: "valid-anon-key",
			},
			expectError: true,
		},
		{
			name: "Invalid URL Format",
			config: &SupabaseConfig{
				URL:        "invalid-url",
				AnonKey:    "valid-anon-key",
				ProjectRef: "abcdefghijklmnopqrst",
			},
			expectError: true,
		},
		{
			name: "Invalid Project Ref Format",
			config: &SupabaseConfig{
				URL:        "https://project.supabase.co",
				AnonKey:    "valid-anon-key",
				ProjectRef: "invalid",
			},
			expectError: true,
		},
		{
			name: "Mock URL",
			config: &SupabaseConfig{
				URL:        "https://mock-project.supabase.co",
				AnonKey:    "valid-anon-key",
				ProjectRef: "abcdefghijklmnopqrst",
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				// Check defaults are set
				assert.Equal(t, 30*time.Second, tt.config.Timeout)
				assert.Equal(t, 3, tt.config.MaxRetries)
				assert.Equal(t, 1*time.Second, tt.config.RetryDelay)
				assert.Equal(t, 100, tt.config.RateLimitRPS)
			}
		})
	}
}

// TestUser_HelperMethods tests user helper methods
// Task 2.3: Test user helper methods
func TestUser_HelperMethods(t *testing.T) {
	user := &User{
		ID:             "550e8400-e29b-41d4-a716-************",
		Email:          "<EMAIL>",
		EmailConfirmed: true,
		Role:           "authenticated",
		UserMetadata: map[string]interface{}{
			"name": "Test User",
			"age":  30,
		},
	}

	// Test GetUserDisplayName
	displayName := user.GetUserDisplayName()
	assert.Contains(t, displayName, "us***@domain.com")

	// Test IsActive
	assert.True(t, user.IsActive())

	// Test HasRole
	assert.True(t, user.HasRole("authenticated"))
	assert.False(t, user.HasRole("admin"))

	// Test GetMetadataValue
	name, exists := user.GetMetadataValue("name")
	assert.True(t, exists)
	assert.Equal(t, "Test User", name)

	_, exists = user.GetMetadataValue("nonexistent")
	assert.False(t, exists)

	// Test SetMetadataValue
	user.SetMetadataValue("location", "Cairo")
	location, exists := user.GetMetadataValue("location")
	assert.True(t, exists)
	assert.Equal(t, "Cairo", location)
}

// TestAuthResponse_HelperMethods tests auth response helper methods
// Task 2.3: Test auth response helper methods
func TestAuthResponse_HelperMethods(t *testing.T) {
	authResp := &AuthResponse{
		AccessToken:  "valid-access-token",
		RefreshToken: "valid-refresh-token",
		ExpiresIn:    3600,
		TokenType:    "Bearer",
		ExpiresAt:    time.Now().Add(time.Hour),
		User: &User{
			ID:    "550e8400-e29b-41d4-a716-************",
			Email: "<EMAIL>",
		},
	}

	// Test IsExpired
	assert.False(t, authResp.IsExpired())

	// Test TimeUntilExpiry
	timeUntil := authResp.TimeUntilExpiry()
	assert.True(t, timeUntil > 0)

	// Test SanitizeForLogging
	sanitized := authResp.SanitizeForLogging()
	assert.Equal(t, "Bearer", sanitized["token_type"])
	assert.Equal(t, 3600, sanitized["expires_in"])
	assert.Contains(t, sanitized["user_email"], "us***@domain.com")

	// Test expired token
	expiredResp := &AuthResponse{
		ExpiresAt: time.Now().Add(-time.Hour),
	}
	assert.True(t, expiredResp.IsExpired())
}
