# CarNow Backend Configuration

## Task 2.4: Configuration Management

This directory contains configuration files for different environments of the CarNow backend application.

## Configuration Files

### 1. `config.yaml` (Default/Development)
- Default configuration for development environment
- Contains safe defaults for local development
- Uses localhost for database and Redis connections

### 2. `config.test.yaml` (Testing)
- Configuration optimized for testing environment
- Reduced connection pools and timeouts for faster tests
- Test-specific database and Redis settings

### 3. `config.production.yaml` (Production)
- Production-ready configuration with environment variables
- Enhanced security settings and connection pools
- All sensitive values use environment variables

## Environment Variables

### Required Environment Variables for Production

```bash
# Application
CARNOW_APP_ENVIRONMENT=production
CARNOW_FRONTEND_URL=https://carnow.ly

# Database (Supabase)
CARNOW_DATABASE_HOST=db.lpxtghyvxuenyyisrrro.supabase.co
CARNOW_DATABASE_USERNAME=postgres
CARNOW_DATABASE_PASSWORD=your_database_password

# Supabase Configuration
CARNOW_SUPABASE_URL=https://lpxtghyvxuenyyisrrro.supabase.co
CARNOW_SUPABASE_ANON_KEY=your_supabase_anon_key
CARNOW_SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
CARNOW_SUPABASE_JWT_SECRET=your_supabase_jwt_secret

# JWT Configuration
CARNOW_JWT_SECRET=your_jwt_secret_at_least_32_characters
CARNOW_JWT_ISSUER=carnow-backend
CARNOW_JWT_AUDIENCE=carnow-app

# Security
CARNOW_SECURITY_ENCRYPTION_KEY=your_encryption_key_32_characters

# Google OAuth
CARNOW_GOOGLE_CLIENT_ID=your_google_client_id
CARNOW_GOOGLE_CLIENT_SECRET=your_google_client_secret
CARNOW_GOOGLE_ANDROID_CLIENT_ID=your_android_client_id
CARNOW_GOOGLE_WEB_REDIRECT_URL=https://carnow.ly/auth/callback
CARNOW_GOOGLE_MOBILE_REDIRECT_URL=carnow://auth/callback

# Redis (Optional)
CARNOW_REDIS_URL=redis://localhost:6379
CARNOW_REDIS_PASSWORD=your_redis_password

# SMS Provider (Twilio)
CARNOW_SMS_PROVIDER=twilio
CARNOW_TWILIO_ACCOUNT_SID=your_twilio_account_sid
CARNOW_TWILIO_AUTH_TOKEN=your_twilio_auth_token
CARNOW_TWILIO_FROM_NUMBER=+**********

# Email Provider
CARNOW_EMAIL_PROVIDER=smtp
CARNOW_EMAIL_FROM_EMAIL=<EMAIL>
CARNOW_EMAIL_FROM_NAME=CarNow
CARNOW_SMTP_HOST=smtp.gmail.com
CARNOW_SMTP_USERNAME=your_smtp_username
CARNOW_SMTP_PASSWORD=your_smtp_password
CARNOW_SENDGRID_API_KEY=your_sendgrid_api_key
```

## Configuration Loading

The application loads configuration in the following order:

1. **Default values** from the configuration struct
2. **Configuration file** (config.yaml, config.test.yaml, or config.production.yaml)
3. **Environment variables** (override file values)

## Environment Detection

The application automatically detects the environment based on:

1. `CARNOW_APP_ENVIRONMENT` environment variable
2. `GO_ENV` environment variable
3. Defaults to "development"

## Configuration Validation

### Supabase Configuration Validation

The application validates Supabase configuration with the following checks:

- **URL Format**: Must be a valid HTTPS URL with .supabase.co domain
- **Mock Data Detection**: Prevents use of mock/test data in production (Forever Plan compliance)
- **Key Length**: Validates minimum length for anon key, service role key, and JWT secret
- **Required Fields**: Ensures all required Supabase fields are present

### Forever Plan Compliance

The configuration system enforces Forever Plan principles:

- **Zero Mock Data**: Detects and prevents mock data patterns
- **Real Data Only**: All configuration must use real Supabase credentials
- **Simple Architecture**: No complex dual-database or sync configurations
- **Production Ready**: Enhanced validation for production environments

## Usage Examples

### Development
```bash
# Run with default development configuration
go run main.go

# Or explicitly set environment
CARNOW_APP_ENVIRONMENT=development go run main.go
```

### Testing
```bash
# Run with test configuration
CARNOW_APP_ENVIRONMENT=test go test ./...

# Or use test config file
CONFIG_FILE=configs/config.test.yaml go run main.go
```

### Production
```bash
# Set all required environment variables
export CARNOW_APP_ENVIRONMENT=production
export CARNOW_SUPABASE_URL=https://lpxtghyvxuenyyisrrro.supabase.co
# ... set other variables

# Run application
go run main.go
```

## Health Checks

The configuration system includes health checks for:

- **Supabase Connectivity**: Tests REST API accessibility
- **Database Connection**: Validates database connectivity through Supabase
- **Redis Connection**: Tests Redis connectivity (if enabled)

Access health checks at:
- `GET /health` - Overall system health
- `GET /health/supabase` - Supabase-specific health
- `GET /health/database` - Database connectivity

## Security Considerations

### Production Security

- All sensitive values use environment variables
- JWT secrets must be at least 32 characters
- Encryption keys must be exactly 32 characters
- HTTPS is required for all external URLs
- CORS is properly configured for frontend domains

### Development Security

- Default values are safe for local development
- Test credentials are clearly marked
- Mock data detection prevents accidental production use

## Troubleshooting

### Common Issues

1. **"Supabase URL is required"**
   - Set `CARNOW_SUPABASE_URL` environment variable
   - Ensure URL format is correct (https://project.supabase.co)

2. **"Mock data detected"**
   - Remove any test/mock patterns from configuration
   - Use real Supabase credentials only

3. **"Invalid URL format"**
   - Ensure URLs start with https://
   - Verify Supabase domain (.supabase.co)

4. **"JWT secret too short"**
   - Use at least 32 characters for JWT secrets
   - Generate secure random strings for production

### Debug Mode

Enable debug logging to see configuration loading:

```bash
CARNOW_APP_DEBUG=true go run main.go
```

This will show:
- Configuration file loading
- Environment variable resolution
- Validation results
- Health check status

## Task 2.4 Completion

✅ **Configuration Enhancement Complete**

- Enhanced Supabase configuration validation
- Added support for multiple environments (development, test, production)
- Implemented comprehensive environment variable support
- Added Forever Plan compliance validation
- Created production-ready configuration files
- Documented all configuration options and usage

The configuration system now supports robust validation, multiple environments, and production-grade security while maintaining Forever Plan compliance with zero tolerance for mock data.
