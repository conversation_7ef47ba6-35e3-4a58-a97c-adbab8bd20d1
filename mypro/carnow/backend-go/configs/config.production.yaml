# CarNow Backend Production Configuration
# Task 2.4: Production configuration with environment variables

app:
  name: "CarNow Backend"
  version: "1.0.0"
  environment: "production"
  debug: false
  timezone: "Africa/Tripoli"
  frontend_url: "${CARNOW_FRONTEND_URL}"

server:
  host: "0.0.0.0"
  port: 8080  # Will be overridden by PORT env var in Render.com
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  max_header_bytes: 1048576
  graceful_timeout: "30s"

database:
  host: "${CARNOW_DATABASE_HOST}"
  port: 6543
  username: "${CARNOW_DATABASE_USERNAME}"
  password: "${CARNOW_DATABASE_PASSWORD}"
  database: "postgres"
  ssl_mode: "require"
  max_open_conns: 25
  max_idle_conns: 10
  conn_max_lifetime: "1h"
  conn_max_idle_time: "30m"

redis:
  enabled: true
  addrs: ["${CARNOW_REDIS_URL}"]
  password: "${CARNOW_REDIS_PASSWORD}"
  db: 0
  pool_size: 20
  min_idle_conns: 10
  max_retries: 5
  dial_timeout: "10s"
  read_timeout: "5s"
  write_timeout: "5s"
  idle_timeout: "10m"
  default_ttl: "1h"
  key_prefix: "carnow:"

supabase:
  url: "${CARNOW_SUPABASE_URL}"
  anon_key: "${CARNOW_SUPABASE_ANON_KEY}"
  service_role_key: "${CARNOW_SUPABASE_SERVICE_ROLE_KEY}"
  jwt_secret: "${CARNOW_SUPABASE_JWT_SECRET}"
  project_ref: "lpxtghyvxuenyyisrrro"

google:
  client_id: "${CARNOW_GOOGLE_CLIENT_ID}"
  client_secret: "${CARNOW_GOOGLE_CLIENT_SECRET}"
  android_client_id: "${CARNOW_GOOGLE_ANDROID_CLIENT_ID}"
  web_redirect_url: "${CARNOW_GOOGLE_WEB_REDIRECT_URL}"
  mobile_redirect_url: "${CARNOW_GOOGLE_MOBILE_REDIRECT_URL}"

jwt:
  secret: "${CARNOW_JWT_SECRET}"
  expires_in: "168h"  # 7 days for better UX
  refresh_expires_in: "720h"  # 30 days
  issuer: "${CARNOW_JWT_ISSUER}"
  audience: "${CARNOW_JWT_AUDIENCE}"
  algorithm: "RS256"

security:
  encryption_key: "${CARNOW_SECURITY_ENCRYPTION_KEY}"
  rate_limit_requests: 1000
  rate_limit_window: "1m"
  cors_allowed_origins: ["${CARNOW_FRONTEND_URL}", "https://carnow.ly"]
  cors_allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  cors_allowed_headers: ["Origin", "Content-Type", "Accept", "Authorization"]
  cors_allow_credentials: true
  
  rate_limit:
    enabled: true
    requests_limit: 1000
    window_size: 60
    login_attempts: 5
    register_attempts: 3
    window_minutes: 15
  
  cors:
    enabled: true
    allowed_origins: ["${CARNOW_FRONTEND_URL}"]

logging:
  level: "info"
  format: "json"
  output: "stdout"
  file_path: ""
  max_size: 100
  max_backups: 5
  max_age: 30
  compress: true

features:
  enable_swagger: false  # Disabled in production for security
  enable_metrics: true
  enable_health_checks: true
  enable_graceful_shutdown: true
  # Forever Plan: These must be false
  enable_complex_auth: false
  enable_dual_database: false
  enable_sync_services: false
  enable_enhanced_features: false

sms:
  provider: "${CARNOW_SMS_PROVIDER}"
  twilio_account_sid: "${CARNOW_TWILIO_ACCOUNT_SID}"
  twilio_auth_token: "${CARNOW_TWILIO_AUTH_TOKEN}"
  twilio_from_number: "${CARNOW_TWILIO_FROM_NUMBER}"

email:
  provider: "${CARNOW_EMAIL_PROVIDER}"
  from_email: "${CARNOW_EMAIL_FROM_EMAIL}"
  from_name: "${CARNOW_EMAIL_FROM_NAME}"
  smtp_host: "${CARNOW_SMTP_HOST}"
  smtp_port: 587
  smtp_username: "${CARNOW_SMTP_USERNAME}"
  smtp_password: "${CARNOW_SMTP_PASSWORD}"
  sendgrid_api_key: "${CARNOW_SENDGRID_API_KEY}"

mfa:
  enabled: true
  max_concurrent_sessions: 5
  session_timeout: "8h"
  inactivity_timeout: "1h"
  require_mfa_for_admin: true
