# CarNow Backend Test Configuration
# Task 2.4: Test configuration for different environments

app:
  name: "CarNow Backend Test"
  version: "1.0.0"
  environment: "test"
  debug: true
  timezone: "Africa/Tripoli"
  frontend_url: "http://localhost:3000"

server:
  host: "localhost"
  port: 8081
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"
  max_header_bytes: 1048576
  graceful_timeout: "15s"

database:
  host: "localhost"
  port: 5432
  username: "postgres"
  password: "test_password"
  database: "carnow_test"
  ssl_mode: "disable"
  max_open_conns: 5
  max_idle_conns: 2
  conn_max_lifetime: "30m"
  conn_max_idle_time: "15m"

redis:
  enabled: true
  addrs: ["localhost:6379"]
  password: ""
  db: 1
  pool_size: 5
  min_idle_conns: 2
  max_retries: 3
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"
  idle_timeout: "5m"
  default_ttl: "30m"
  key_prefix: "carnow_test:"

supabase:
  url: "https://lpxtghyvxuenyyisrrro.supabase.co"
  anon_key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU2NzI4MDAsImV4cCI6MjA1MTI0ODgwMH0.test"
  service_role_key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNTY3MjgwMCwiZXhwIjoyMDUxMjQ4ODAwfQ.test"
  jwt_secret: "test-jwt-secret-that-is-at-least-32-characters-long-for-testing"
  project_ref: "lpxtghyvxuenyyisrrro"

google:
  client_id: "test-google-client-id"
  client_secret: "test-google-client-secret"
  android_client_id: "test-android-client-id"
  web_redirect_url: "http://localhost:3000/auth/callback"
  mobile_redirect_url: "carnow://auth/callback"

jwt:
  secret: "test-jwt-secret-that-is-at-least-32-characters-long-for-testing"
  expires_in: "15m"
  refresh_expires_in: "24h"
  issuer: "carnow-backend-test"
  audience: "carnow-app-test"
  algorithm: "HS256"

security:
  encryption_key: "test-encryption-key-that-is-at-least-32-characters-long"
  rate_limit_requests: 50
  rate_limit_window: "1m"
  cors_allowed_origins: ["http://localhost:3000", "http://localhost:3001"]
  cors_allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  cors_allowed_headers: ["Origin", "Content-Type", "Accept", "Authorization"]
  cors_allow_credentials: true
  
  rate_limit:
    enabled: true
    requests_limit: 50
    window_size: 60
    login_attempts: 3
    register_attempts: 2
    window_minutes: 10
  
  cors:
    enabled: true
    allowed_origins: ["http://localhost:3000"]

logging:
  level: "debug"
  format: "json"
  output: "stdout"
  file_path: "/tmp/carnow_test.log"
  max_size: 50
  max_backups: 2
  max_age: 7
  compress: false

features:
  enable_swagger: true
  enable_metrics: true
  enable_health_checks: true
  enable_graceful_shutdown: true
  # Forever Plan: These must be false
  enable_complex_auth: false
  enable_dual_database: false
  enable_sync_services: false
  enable_enhanced_features: false

sms:
  provider: "twilio"
  twilio_account_sid: "test_account_sid"
  twilio_auth_token: "test_auth_token"
  twilio_from_number: "+**********"

email:
  provider: "smtp"
  from_email: "<EMAIL>"
  from_name: "CarNow Test"
  smtp_host: "localhost"
  smtp_port: 1025
  smtp_username: "test"
  smtp_password: "test"

mfa:
  enabled: false
  max_concurrent_sessions: 3
  session_timeout: "30m"
  inactivity_timeout: "15m"
  require_mfa_for_admin: false
