# CarNow Backend - Production Dockerfile
# Multi-stage build for optimized production image

# Build stage
FROM golang:1.21-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download && go mod verify

# Copy source code
COPY . .

# Build the application with optimizations
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o carnow-backend \
    ./cmd/main.go

# Production stage
FROM alpine:latest

# Install runtime dependencies
RUN apk --no-cache add ca-certificates tzdata

# Create non-root user for security
RUN addgroup -g 1001 -S carnow && \
    adduser -u 1001 -S carnow -G carnow

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/carnow-backend .

# Copy configuration files (if any)
COPY --from=builder /app/configs ./configs

# Set ownership
RUN chown -R carnow:carnow /app

# Switch to non-root user
USER carnow

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Set environment variables
ENV CARNOW_ENV=production
ENV CARNOW_SERVER_HOST=0.0.0.0
ENV CARNOW_SERVER_PORT=8080

# Run the application
CMD ["./carnow-backend"]