# 🚀 CarNow Backend - Development

## 🔧 للتطوير المحلي

```bash
# تشغيل Backend في وضع التطوير
./run-dev.sh
```

### ✅ ما يحدث عند التشغيل:

- **Environment**: development
- **Host**: 0.0.0.0 (accessible from Android Emulator)
- **Port**: 8080
- **Debug**: enabled
- **CORS**: enabled
- **Hot reload**: ready (install `air` for automatic restart)

### 📱 الاتصال:

- **Android Emulator**: `http://********:8080`
- **Local Browser**: `http://localhost:8080`

---

## 🔐 Enhanced Authentication System

### **✅ Implementation Status: COMPLETED**

The Enhanced Authentication system with Supabase integration has been successfully implemented and tested.

### **🚀 Available Endpoints**

#### **Authentication Endpoints**
```bash
# Enhanced Login
POST /api/v1/auth/enhanced/login
curl -X POST http://localhost:8080/api/v1/auth/enhanced/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password"}'

# Enhanced Logout
POST /api/v1/auth/enhanced/logout
curl -X POST http://localhost:8080/api/v1/auth/enhanced/logout \
     -H "Authorization: Bearer <token>"

# Enhanced Token Refresh
POST /api/v1/auth/enhanced/refresh
curl -X POST http://localhost:8080/api/v1/auth/enhanced/refresh \
     -H "Content-Type: application/json" \
     -d '{"refresh_token":"<refresh_token>"}'

# Enhanced User Profile
GET /api/v1/auth/enhanced/user
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/v1/auth/enhanced/user
```

#### **Protected Endpoints**
```bash
# Protected Profile
GET /api/v1/protected/profile
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/v1/protected/profile

# Protected Dashboard
GET /api/v1/protected/dashboard
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/v1/protected/dashboard
```

#### **Health Check Endpoints**
```bash
# Enhanced Auth Health
GET /health/enhanced-auth
curl http://localhost:8080/health/enhanced-auth

# Supabase Auth Health
GET /health/supabase-auth
curl http://localhost:8080/health/supabase-auth

# General Health
GET /health
curl http://localhost:8080/health
```

### **🔧 Required Environment Variables**
```bash
# Supabase Configuration
CARNOW_SUPABASE_URL=https://lpxtghyvxuenyyisrrro.supabase.co
CARNOW_SUPABASE_ANON_KEY=your-supabase-anon-key
CARNOW_SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
CARNOW_SUPABASE_PROJECT_REF=lpxtghyvxuenyyisrrro

# JWT Configuration
CARNOW_JWT_SECRET=your-secure-jwt-secret
CARNOW_JWT_EXPIRY=24h

# Google OAuth
CARNOW_GOOGLE_CLIENT_ID=your-google-client-id
CARNOW_GOOGLE_CLIENT_SECRET=your-google-client-secret
```

### **✅ Forever Plan Compliance**
- ✅ **Real Data Only**: All authentication data from Supabase database
- ✅ **Zero Mock Data**: Complete prohibition with detection and prevention
- ✅ **Production Excellence**: Enhanced error handling and security
- ✅ **Comprehensive Testing**: All core functionality tested and validated
- ✅ **Security First**: JWT validation, rate limiting, and input sanitization

### **🧪 Testing**
```bash
# Run all tests
go test ./... -v

# Run specific test suites
go test ./internal/handlers -v -run TestEnhancedAuthHandlers
go test ./internal/services -v -run TestSupabaseAuthService
go test ./internal/routes -v -run TestEnhancedAuthRoutes

# Run with coverage
go test ./... -coverprofile=coverage.out
go tool cover -html=coverage.out
```

## Project Structure
```
backend-go/
├── cmd/                    # Application entry points
│   ├── api/               # Main API server
│   └── server/            # Server utilities
├── configs/               # Configuration files
├── internal/              # Private application code
│   ├── api/              # API handlers
│   ├── config/           # Configuration management
│   ├── core/             # Core business logic
│   ├── domain/           # Domain models
│   ├── handlers/         # HTTP handlers
│   ├── infrastructure/   # External dependencies
│   ├── modules/          # Feature modules
│   ├── routes/           # Route definitions
│   ├── services/         # Business services
│   └── shared/           # Shared utilities
├── pkg/                   # Public packages
├── tools/                 # Development tools
├── go.mod                 # Go module file
├── go.sum                 # Go dependencies checksum
├── Dockerfile            # Docker configuration
├── render.yaml           # Render deployment config
└── README.md             # This file
```

## Getting Started

### Prerequisites
- Go 1.21 or higher
- PostgreSQL database (via Supabase)
- Redis (optional, for caching)

### Installation
1. Clone the repository:
```bash
git clone https://github.com/AttSee/carnow-backend-go.git
cd carnow-backend-go
```

2. Install dependencies:
```bash
go mod download
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Run the application:
```bash
go run cmd/api/main.go
```

### Configuration
The application uses configuration files in the `configs/` directory:
- `config.yaml` - Default configuration
- `config.local.yaml` - Local development overrides

### Environment Variables
- `SUPABASE_URL` - Supabase project URL
- `SUPABASE_ANON_KEY` - Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase service role key
- `JWT_SECRET` - JWT signing secret
- `PORT` - Server port (default: 8080)
- `ENVIRONMENT` - Environment (development, staging, production)

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/refresh` - Refresh JWT token
- `POST /api/v1/auth/logout` - User logout

### Products
- `GET /api/v1/products` - List products
- `GET /api/v1/products/:id` - Get product details
- `POST /api/v1/products` - Create product
- `PUT /api/v1/products/:id` - Update product
- `DELETE /api/v1/products/:id` - Delete product

### Orders
- `GET /api/v1/orders` - List orders
- `GET /api/v1/orders/:id` - Get order details
- `POST /api/v1/orders` - Create order
- `PUT /api/v1/orders/:id` - Update order status

### Cart
- `GET /api/v1/cart` - Get user cart
- `POST /api/v1/cart/items` - Add item to cart
- `PUT /api/v1/cart/items/:id` - Update cart item
- `DELETE /api/v1/cart/items/:id` - Remove item from cart

## Development

### Running Tests
```bash
go test ./...
```

### Code Generation
```bash
go generate ./...
```

### Building
```bash
go build -o bin/server cmd/api/main.go
```

### Docker
```bash
docker build -t carnow-backend .
docker run -p 8080:8080 carnow-backend
```

## Deployment

### Render.com
The application is automatically deployed to Render.com when changes are pushed to the main branch.

**Deployment URL**: https://backend-go-8klm.onrender.com

### Manual Deployment
1. Build the application:
```bash
go build -o bin/server cmd/api/main.go
```

2. Deploy to your preferred platform

## Database Schema
The application uses Supabase as the database layer. Key schemas:
- `public.*` - General application data
- `finance.*` - Financial data
- `audit.*` - Logs and audit trails

## Security
- JWT-based authentication
- Role-based access control (RBAC)
- Input validation and sanitization
- SQL injection prevention via parameterized queries
- CORS configuration

## Monitoring
- Application logs via structured logging
- Health check endpoint: `GET /health`
- Metrics collection (if configured)

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License
This project is proprietary software for CarNow.

## Support
For support and questions, please contact the development team.

---

**Last Updated**: July 29, 2025
**Version**: 1.0.0 