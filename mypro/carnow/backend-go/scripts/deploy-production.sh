#!/bin/bash

# CarNow Backend - Production Deployment Script
# This script handles the complete deployment process for production

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NAMESPACE="carnow-production"
APP_NAME="carnow-backend"
IMAGE_TAG="${IMAGE_TAG:-latest}"
REGISTRY="${REGISTRY:-carnow-registry}"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "docker is not installed"
        exit 1
    fi
    
    # Check if we can connect to Kubernetes cluster
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check if namespace exists
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_warning "Namespace $NAMESPACE does not exist, creating..."
        kubectl create namespace "$NAMESPACE"
        kubectl label namespace "$NAMESPACE" name="$NAMESPACE"
    fi
    
    log_success "Prerequisites check passed"
}

validate_configuration() {
    log_info "Validating configuration..."
    
    # Check if .env.production exists
    if [[ ! -f "$PROJECT_ROOT/.env.production" ]]; then
        log_error ".env.production file not found"
        log_info "Please copy .env.production.example to .env.production and configure it"
        exit 1
    fi
    
    # Source environment variables
    set -a
    source "$PROJECT_ROOT/.env.production"
    set +a
    
    # Validate required environment variables
    required_vars=(
        "CARNOW_SUPABASE_URL"
        "CARNOW_SUPABASE_ANON_KEY"
        "CARNOW_SUPABASE_SERVICE_ROLE_KEY"
        "CARNOW_JWT_SECRET"
        "CARNOW_GOOGLE_CLIENT_ID"
        "CARNOW_GOOGLE_CLIENT_SECRET"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "Required environment variable $var is not set"
            exit 1
        fi
    done
    
    # Validate JWT secret length
    if [[ ${#CARNOW_JWT_SECRET} -lt 32 ]]; then
        log_error "JWT secret must be at least 32 characters long"
        exit 1
    fi
    
    # Validate Supabase URL format
    if [[ ! "$CARNOW_SUPABASE_URL" =~ ^https?:// ]]; then
        log_error "Supabase URL must be a valid HTTP/HTTPS URL"
        exit 1
    fi
    
    log_success "Configuration validation passed"
}

build_and_push_image() {
    log_info "Building and pushing Docker image..."
    
    # Build image
    log_info "Building Docker image..."
    docker build -t "$REGISTRY/$APP_NAME:$IMAGE_TAG" "$PROJECT_ROOT"
    
    # Tag as latest
    docker tag "$REGISTRY/$APP_NAME:$IMAGE_TAG" "$REGISTRY/$APP_NAME:latest"
    
    # Push image
    log_info "Pushing Docker image to registry..."
    docker push "$REGISTRY/$APP_NAME:$IMAGE_TAG"
    docker push "$REGISTRY/$APP_NAME:latest"
    
    log_success "Docker image built and pushed successfully"
}

create_secrets() {
    log_info "Creating Kubernetes secrets..."
    
    # Source environment variables
    set -a
    source "$PROJECT_ROOT/.env.production"
    set +a
    
    # Create secrets
    kubectl create secret generic carnow-secrets \
        --namespace="$NAMESPACE" \
        --from-literal=supabase-anon-key="$CARNOW_SUPABASE_ANON_KEY" \
        --from-literal=supabase-service-role-key="$CARNOW_SUPABASE_SERVICE_ROLE_KEY" \
        --from-literal=jwt-secret="$CARNOW_JWT_SECRET" \
        --from-literal=google-client-secret="$CARNOW_GOOGLE_CLIENT_SECRET" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "Kubernetes secrets created successfully"
}

deploy_application() {
    log_info "Deploying application to Kubernetes..."
    
    # Apply Kubernetes manifests
    kubectl apply -f "$PROJECT_ROOT/k8s/deployment.yaml"
    kubectl apply -f "$PROJECT_ROOT/k8s/service.yaml"
    
    # Wait for deployment to be ready
    log_info "Waiting for deployment to be ready..."
    kubectl rollout status deployment/"$APP_NAME" --namespace="$NAMESPACE" --timeout=300s
    
    log_success "Application deployed successfully"
}

run_health_checks() {
    log_info "Running health checks..."
    
    # Get service endpoint
    SERVICE_IP=$(kubectl get service carnow-backend-service --namespace="$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [[ -z "$SERVICE_IP" ]]; then
        SERVICE_IP=$(kubectl get service carnow-backend-service --namespace="$NAMESPACE" -o jsonpath='{.spec.clusterIP}')
    fi
    
    # Wait for service to be ready
    sleep 30
    
    # Test health endpoints
    log_info "Testing health endpoints..."
    
    # Port forward for testing
    kubectl port-forward service/carnow-backend-service 8080:80 --namespace="$NAMESPACE" &
    PORT_FORWARD_PID=$!
    sleep 5
    
    # Test general health
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log_success "General health check passed"
    else
        log_error "General health check failed"
        kill $PORT_FORWARD_PID
        exit 1
    fi
    
    # Test enhanced auth health
    if curl -f http://localhost:8080/health/enhanced-auth > /dev/null 2>&1; then
        log_success "Enhanced auth health check passed"
    else
        log_error "Enhanced auth health check failed"
        kill $PORT_FORWARD_PID
        exit 1
    fi
    
    # Test Supabase auth health
    if curl -f http://localhost:8080/health/supabase-auth > /dev/null 2>&1; then
        log_success "Supabase auth health check passed"
    else
        log_error "Supabase auth health check failed"
        kill $PORT_FORWARD_PID
        exit 1
    fi
    
    # Clean up port forward
    kill $PORT_FORWARD_PID
    
    log_success "All health checks passed"
}

test_authentication() {
    log_info "Testing authentication endpoints..."
    
    # Port forward for testing
    kubectl port-forward service/carnow-backend-service 8080:80 --namespace="$NAMESPACE" &
    PORT_FORWARD_PID=$!
    sleep 5
    
    # Test mock data detection (should fail)
    log_info "Testing mock data detection..."
    if curl -X POST http://localhost:8080/api/v1/auth/enhanced/login \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"password"}' \
        -s | grep -q "MOCK_DATA_DETECTED"; then
        log_success "Mock data detection working correctly"
    else
        log_error "Mock data detection not working"
        kill $PORT_FORWARD_PID
        exit 1
    fi
    
    # Clean up port forward
    kill $PORT_FORWARD_PID
    
    log_success "Authentication tests passed"
}

show_deployment_info() {
    log_info "Deployment Information:"
    echo "=========================="
    echo "Namespace: $NAMESPACE"
    echo "Application: $APP_NAME"
    echo "Image Tag: $IMAGE_TAG"
    echo ""
    
    log_info "Service Information:"
    kubectl get services --namespace="$NAMESPACE"
    echo ""
    
    log_info "Pod Information:"
    kubectl get pods --namespace="$NAMESPACE"
    echo ""
    
    log_info "Ingress Information:"
    kubectl get ingress --namespace="$NAMESPACE"
    echo ""
    
    log_info "Useful Commands:"
    echo "View logs: kubectl logs -f deployment/$APP_NAME --namespace=$NAMESPACE"
    echo "Port forward: kubectl port-forward service/carnow-backend-service 8080:80 --namespace=$NAMESPACE"
    echo "Scale deployment: kubectl scale deployment/$APP_NAME --replicas=5 --namespace=$NAMESPACE"
    echo "Rollback deployment: kubectl rollout undo deployment/$APP_NAME --namespace=$NAMESPACE"
}

cleanup_on_error() {
    log_error "Deployment failed, cleaning up..."
    
    # Kill any background processes
    if [[ -n "${PORT_FORWARD_PID:-}" ]]; then
        kill $PORT_FORWARD_PID 2>/dev/null || true
    fi
    
    # Optionally rollback deployment
    if [[ "${ROLLBACK_ON_ERROR:-false}" == "true" ]]; then
        log_info "Rolling back deployment..."
        kubectl rollout undo deployment/"$APP_NAME" --namespace="$NAMESPACE" || true
    fi
}

# Main deployment process
main() {
    log_info "Starting CarNow Backend production deployment..."
    
    # Set up error handling
    trap cleanup_on_error ERR
    
    # Run deployment steps
    check_prerequisites
    validate_configuration
    build_and_push_image
    create_secrets
    deploy_application
    run_health_checks
    test_authentication
    show_deployment_info
    
    log_success "🎉 CarNow Backend deployed successfully to production!"
    log_info "The application is now running and ready to serve requests."
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --image-tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --registry)
            REGISTRY="$2"
            shift 2
            ;;
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --rollback-on-error)
            ROLLBACK_ON_ERROR=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --image-tag TAG          Docker image tag (default: latest)"
            echo "  --registry REGISTRY      Docker registry (default: carnow-registry)"
            echo "  --namespace NAMESPACE    Kubernetes namespace (default: carnow-production)"
            echo "  --rollback-on-error      Rollback deployment on error"
            echo "  --help                   Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main
