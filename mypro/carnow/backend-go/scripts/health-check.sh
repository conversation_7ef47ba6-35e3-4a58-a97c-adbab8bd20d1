#!/bin/bash

# CarNow Backend - Production Health Check Script
# Comprehensive health monitoring for production deployment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NAMESPACE="carnow-production"
APP_NAME="carnow-backend"
SERVICE_NAME="carnow-backend-service"

# Health check endpoints
HEALTH_ENDPOINTS=(
    "/health"
    "/health/enhanced-auth"
    "/health/supabase-auth"
)

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    # Check if curl is installed
    if ! command -v curl &> /dev/null; then
        log_error "curl is not installed"
        exit 1
    fi
    
    # Check if we can connect to Kubernetes cluster
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

check_kubernetes_resources() {
    log_info "Checking Kubernetes resources..."
    
    local all_healthy=true
    
    # Check namespace
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_success "Namespace $NAMESPACE exists"
    else
        log_error "Namespace $NAMESPACE does not exist"
        all_healthy=false
    fi
    
    # Check deployment
    if kubectl get deployment "$APP_NAME" --namespace="$NAMESPACE" &> /dev/null; then
        local ready_replicas=$(kubectl get deployment "$APP_NAME" --namespace="$NAMESPACE" -o jsonpath='{.status.readyReplicas}')
        local desired_replicas=$(kubectl get deployment "$APP_NAME" --namespace="$NAMESPACE" -o jsonpath='{.spec.replicas}')
        
        if [[ "$ready_replicas" == "$desired_replicas" ]]; then
            log_success "Deployment $APP_NAME is healthy ($ready_replicas/$desired_replicas replicas ready)"
        else
            log_error "Deployment $APP_NAME is unhealthy ($ready_replicas/$desired_replicas replicas ready)"
            all_healthy=false
        fi
    else
        log_error "Deployment $APP_NAME does not exist"
        all_healthy=false
    fi
    
    # Check service
    if kubectl get service "$SERVICE_NAME" --namespace="$NAMESPACE" &> /dev/null; then
        log_success "Service $SERVICE_NAME exists"
    else
        log_error "Service $SERVICE_NAME does not exist"
        all_healthy=false
    fi
    
    # Check pods
    local pod_count=$(kubectl get pods --namespace="$NAMESPACE" -l app="$APP_NAME" --field-selector=status.phase=Running -o name | wc -l)
    if [[ "$pod_count" -gt 0 ]]; then
        log_success "$pod_count pods are running"
    else
        log_error "No pods are running"
        all_healthy=false
    fi
    
    if [[ "$all_healthy" == "false" ]]; then
        return 1
    fi
}

check_pod_health() {
    log_info "Checking individual pod health..."
    
    local pods=$(kubectl get pods --namespace="$NAMESPACE" -l app="$APP_NAME" -o jsonpath='{.items[*].metadata.name}')
    local all_healthy=true
    
    for pod in $pods; do
        # Check pod status
        local pod_status=$(kubectl get pod "$pod" --namespace="$NAMESPACE" -o jsonpath='{.status.phase}')
        
        if [[ "$pod_status" == "Running" ]]; then
            log_success "Pod $pod is running"
            
            # Check container readiness
            local ready=$(kubectl get pod "$pod" --namespace="$NAMESPACE" -o jsonpath='{.status.containerStatuses[0].ready}')
            if [[ "$ready" == "true" ]]; then
                log_success "Pod $pod is ready"
            else
                log_error "Pod $pod is not ready"
                all_healthy=false
            fi
        else
            log_error "Pod $pod is in $pod_status state"
            all_healthy=false
        fi
    done
    
    if [[ "$all_healthy" == "false" ]]; then
        return 1
    fi
}

test_health_endpoints() {
    log_info "Testing health endpoints..."
    
    # Start port forward
    kubectl port-forward service/"$SERVICE_NAME" 8080:80 --namespace="$NAMESPACE" &
    local port_forward_pid=$!
    
    # Wait for port forward to be ready
    sleep 5
    
    local all_healthy=true
    
    # Test each health endpoint
    for endpoint in "${HEALTH_ENDPOINTS[@]}"; do
        log_info "Testing endpoint: $endpoint"
        
        local response_code=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8080$endpoint" || echo "000")
        
        if [[ "$response_code" == "200" ]]; then
            log_success "Endpoint $endpoint returned 200 OK"
            
            # Get response body for detailed check
            local response_body=$(curl -s "http://localhost:8080$endpoint")
            
            # Check if response contains expected fields
            if echo "$response_body" | grep -q '"status"'; then
                log_success "Endpoint $endpoint has valid response format"
            else
                log_warning "Endpoint $endpoint response format may be invalid"
            fi
        else
            log_error "Endpoint $endpoint returned $response_code"
            all_healthy=false
        fi
    done
    
    # Clean up port forward
    kill $port_forward_pid 2>/dev/null || true
    
    if [[ "$all_healthy" == "false" ]]; then
        return 1
    fi
}

test_authentication_endpoints() {
    log_info "Testing authentication endpoints..."
    
    # Start port forward
    kubectl port-forward service/"$SERVICE_NAME" 8080:80 --namespace="$NAMESPACE" &
    local port_forward_pid=$!
    
    # Wait for port forward to be ready
    sleep 5
    
    local all_healthy=true
    
    # Test mock data detection (should return 400 with MOCK_DATA_DETECTED)
    log_info "Testing mock data detection..."
    local auth_response=$(curl -s -X POST "http://localhost:8080/api/v1/auth/enhanced/login" \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"password"}')
    
    if echo "$auth_response" | grep -q "MOCK_DATA_DETECTED"; then
        log_success "Mock data detection is working correctly"
    else
        log_error "Mock data detection is not working"
        all_healthy=false
    fi
    
    # Test invalid credentials (should return 400 with INVALID_CREDENTIALS)
    log_info "Testing invalid credentials handling..."
    local invalid_response=$(curl -s -X POST "http://localhost:8080/api/v1/auth/enhanced/login" \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"wrongpassword"}')
    
    if echo "$invalid_response" | grep -q "INVALID_CREDENTIALS\|USER_NOT_FOUND"; then
        log_success "Invalid credentials handling is working correctly"
    else
        log_error "Invalid credentials handling is not working"
        all_healthy=false
    fi
    
    # Test protected endpoint without token (should return 401)
    log_info "Testing protected endpoint without token..."
    local protected_response_code=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8080/api/v1/protected/profile")
    
    if [[ "$protected_response_code" == "401" ]]; then
        log_success "Protected endpoint correctly requires authentication"
    else
        log_error "Protected endpoint is not properly secured (returned $protected_response_code)"
        all_healthy=false
    fi
    
    # Clean up port forward
    kill $port_forward_pid 2>/dev/null || true
    
    if [[ "$all_healthy" == "false" ]]; then
        return 1
    fi
}

check_resource_usage() {
    log_info "Checking resource usage..."
    
    local pods=$(kubectl get pods --namespace="$NAMESPACE" -l app="$APP_NAME" -o jsonpath='{.items[*].metadata.name}')
    
    for pod in $pods; do
        log_info "Resource usage for pod $pod:"
        
        # Get CPU and memory usage
        local metrics=$(kubectl top pod "$pod" --namespace="$NAMESPACE" 2>/dev/null || echo "Metrics not available")
        echo "  $metrics"
    done
}

check_logs_for_errors() {
    log_info "Checking recent logs for errors..."
    
    local pods=$(kubectl get pods --namespace="$NAMESPACE" -l app="$APP_NAME" -o jsonpath='{.items[*].metadata.name}')
    local error_count=0
    
    for pod in $pods; do
        log_info "Checking logs for pod $pod..."
        
        # Get recent logs and count errors
        local recent_errors=$(kubectl logs "$pod" --namespace="$NAMESPACE" --since=5m | grep -i "error\|fatal\|panic" | wc -l)
        
        if [[ "$recent_errors" -gt 0 ]]; then
            log_warning "Found $recent_errors error(s) in recent logs for pod $pod"
            error_count=$((error_count + recent_errors))
            
            # Show last few errors
            log_info "Recent errors:"
            kubectl logs "$pod" --namespace="$NAMESPACE" --since=5m | grep -i "error\|fatal\|panic" | tail -3
        else
            log_success "No recent errors found in logs for pod $pod"
        fi
    done
    
    if [[ "$error_count" -gt 10 ]]; then
        log_error "High error count detected: $error_count errors in recent logs"
        return 1
    elif [[ "$error_count" -gt 0 ]]; then
        log_warning "Some errors detected: $error_count errors in recent logs"
    fi
}

show_summary() {
    log_info "Health Check Summary:"
    echo "====================="
    echo "Namespace: $NAMESPACE"
    echo "Application: $APP_NAME"
    echo "Timestamp: $(date)"
    echo ""
    
    # Show deployment status
    log_info "Deployment Status:"
    kubectl get deployment "$APP_NAME" --namespace="$NAMESPACE"
    echo ""
    
    # Show pod status
    log_info "Pod Status:"
    kubectl get pods --namespace="$NAMESPACE" -l app="$APP_NAME"
    echo ""
    
    # Show service status
    log_info "Service Status:"
    kubectl get service "$SERVICE_NAME" --namespace="$NAMESPACE"
    echo ""
}

cleanup_on_error() {
    # Kill any background processes
    if [[ -n "${port_forward_pid:-}" ]]; then
        kill $port_forward_pid 2>/dev/null || true
    fi
}

# Main health check process
main() {
    log_info "Starting CarNow Backend health check..."
    
    # Set up error handling
    trap cleanup_on_error ERR
    
    local overall_health=true
    
    # Run health checks
    check_prerequisites
    
    if ! check_kubernetes_resources; then
        overall_health=false
    fi
    
    if ! check_pod_health; then
        overall_health=false
    fi
    
    if ! test_health_endpoints; then
        overall_health=false
    fi
    
    if ! test_authentication_endpoints; then
        overall_health=false
    fi
    
    check_resource_usage
    check_logs_for_errors
    show_summary
    
    if [[ "$overall_health" == "true" ]]; then
        log_success "🎉 All health checks passed! CarNow Backend is healthy."
        exit 0
    else
        log_error "❌ Some health checks failed! CarNow Backend may have issues."
        exit 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --namespace NAMESPACE    Kubernetes namespace (default: carnow-production)"
            echo "  --help                   Show this help message"
            echo ""
            echo "This script performs comprehensive health checks on the CarNow Backend deployment."
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main
