#!/bin/bash

# CarNow Backend - Production Rollback Script
# This script handles rollback procedures for production deployment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NAMESPACE="carnow-production"
APP_NAME="carnow-backend"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    # Check if we can connect to Kubernetes cluster
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check if namespace exists
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_error "Namespace $NAMESPACE does not exist"
        exit 1
    fi
    
    # Check if deployment exists
    if ! kubectl get deployment "$APP_NAME" --namespace="$NAMESPACE" &> /dev/null; then
        log_error "Deployment $APP_NAME does not exist in namespace $NAMESPACE"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

show_rollout_history() {
    log_info "Deployment rollout history:"
    kubectl rollout history deployment/"$APP_NAME" --namespace="$NAMESPACE"
    echo ""
}

get_current_revision() {
    kubectl get deployment "$APP_NAME" --namespace="$NAMESPACE" -o jsonpath='{.metadata.annotations.deployment\.kubernetes\.io/revision}'
}

rollback_deployment() {
    local revision=${1:-}
    
    log_info "Current deployment revision: $(get_current_revision)"
    
    if [[ -n "$revision" ]]; then
        log_info "Rolling back to revision $revision..."
        kubectl rollout undo deployment/"$APP_NAME" --namespace="$NAMESPACE" --to-revision="$revision"
    else
        log_info "Rolling back to previous revision..."
        kubectl rollout undo deployment/"$APP_NAME" --namespace="$NAMESPACE"
    fi
    
    # Wait for rollback to complete
    log_info "Waiting for rollback to complete..."
    kubectl rollout status deployment/"$APP_NAME" --namespace="$NAMESPACE" --timeout=300s
    
    log_success "Rollback completed successfully"
    log_info "New deployment revision: $(get_current_revision)"
}

verify_rollback() {
    log_info "Verifying rollback..."
    
    # Wait for pods to be ready
    sleep 30
    
    # Port forward for testing
    kubectl port-forward service/carnow-backend-service 8080:80 --namespace="$NAMESPACE" &
    PORT_FORWARD_PID=$!
    sleep 5
    
    # Test health endpoints
    local health_passed=true
    
    # Test general health
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log_success "General health check passed"
    else
        log_error "General health check failed"
        health_passed=false
    fi
    
    # Test enhanced auth health
    if curl -f http://localhost:8080/health/enhanced-auth > /dev/null 2>&1; then
        log_success "Enhanced auth health check passed"
    else
        log_error "Enhanced auth health check failed"
        health_passed=false
    fi
    
    # Test Supabase auth health
    if curl -f http://localhost:8080/health/supabase-auth > /dev/null 2>&1; then
        log_success "Supabase auth health check passed"
    else
        log_error "Supabase auth health check failed"
        health_passed=false
    fi
    
    # Clean up port forward
    kill $PORT_FORWARD_PID
    
    if [[ "$health_passed" == "true" ]]; then
        log_success "Rollback verification passed"
    else
        log_error "Rollback verification failed"
        exit 1
    fi
}

test_authentication_after_rollback() {
    log_info "Testing authentication after rollback..."
    
    # Port forward for testing
    kubectl port-forward service/carnow-backend-service 8080:80 --namespace="$NAMESPACE" &
    PORT_FORWARD_PID=$!
    sleep 5
    
    # Test mock data detection (should fail)
    log_info "Testing mock data detection..."
    if curl -X POST http://localhost:8080/api/v1/auth/enhanced/login \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"password"}' \
        -s | grep -q "MOCK_DATA_DETECTED"; then
        log_success "Mock data detection working correctly after rollback"
    else
        log_error "Mock data detection not working after rollback"
        kill $PORT_FORWARD_PID
        exit 1
    fi
    
    # Clean up port forward
    kill $PORT_FORWARD_PID
    
    log_success "Authentication tests passed after rollback"
}

show_current_status() {
    log_info "Current deployment status:"
    echo "=========================="
    echo "Namespace: $NAMESPACE"
    echo "Application: $APP_NAME"
    echo "Current Revision: $(get_current_revision)"
    echo ""
    
    log_info "Pod Status:"
    kubectl get pods --namespace="$NAMESPACE" -l app="$APP_NAME"
    echo ""
    
    log_info "Service Status:"
    kubectl get services --namespace="$NAMESPACE"
    echo ""
    
    log_info "Recent Events:"
    kubectl get events --namespace="$NAMESPACE" --sort-by='.lastTimestamp' | tail -10
}

cleanup_on_error() {
    log_error "Rollback process failed, cleaning up..."
    
    # Kill any background processes
    if [[ -n "${PORT_FORWARD_PID:-}" ]]; then
        kill $PORT_FORWARD_PID 2>/dev/null || true
    fi
}

# Main rollback process
main() {
    local revision=${1:-}
    
    log_info "Starting CarNow Backend production rollback..."
    
    # Set up error handling
    trap cleanup_on_error ERR
    
    # Confirmation prompt
    if [[ "${FORCE_ROLLBACK:-false}" != "true" ]]; then
        echo ""
        log_warning "⚠️  You are about to rollback the production deployment!"
        echo ""
        show_rollout_history
        echo ""
        
        if [[ -n "$revision" ]]; then
            read -p "Are you sure you want to rollback to revision $revision? (yes/no): " confirm
        else
            read -p "Are you sure you want to rollback to the previous revision? (yes/no): " confirm
        fi
        
        if [[ "$confirm" != "yes" ]]; then
            log_info "Rollback cancelled by user"
            exit 0
        fi
    fi
    
    # Run rollback steps
    check_prerequisites
    show_rollout_history
    rollback_deployment "$revision"
    verify_rollback
    test_authentication_after_rollback
    show_current_status
    
    log_success "🎉 CarNow Backend rollback completed successfully!"
    log_info "The application has been rolled back and is ready to serve requests."
}

# Parse command line arguments
REVISION=""
while [[ $# -gt 0 ]]; do
    case $1 in
        --revision)
            REVISION="$2"
            shift 2
            ;;
        --force)
            FORCE_ROLLBACK=true
            shift
            ;;
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --revision REVISION      Specific revision to rollback to"
            echo "  --force                  Skip confirmation prompt"
            echo "  --namespace NAMESPACE    Kubernetes namespace (default: carnow-production)"
            echo "  --help                   Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                       Rollback to previous revision"
            echo "  $0 --revision 5          Rollback to revision 5"
            echo "  $0 --force               Rollback without confirmation"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main "$REVISION"
