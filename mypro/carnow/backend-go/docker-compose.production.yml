# CarNow Backend - Production Docker Compose
# For production deployment with monitoring and security

version: '3.8'

services:
  # CarNow Backend Service
  carnow-backend:
    build:
      context: .
      dockerfile: Dockerfile
    image: carnow-backend:latest
    container_name: carnow-backend-prod
    restart: unless-stopped
    
    # Environment variables from file
    env_file:
      - .env.production
    
    # Additional environment variables
    environment:
      - CARNOW_ENV=production
      - CARNOW_DEPLOYMENT_ENV=docker-compose
      - CARNOW_CONTAINER_NAME=carnow-backend-prod
    
    # Port mapping
    ports:
      - "8080:8080"
    
    # Health check
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    
    # Security settings
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=carnow-backend,environment=production"
    
    # Dependencies
    depends_on:
      redis:
        condition: service_healthy
    
    # Networks
    networks:
      - carnow-network

  # Redis Cache Service
  redis:
    image: redis:7-alpine
    container_name: carnow-redis-prod
    restart: unless-stopped
    
    # Redis configuration
    command: redis-server --appendonly yes --requirepass ${CARNOW_REDIS_PASSWORD:-}
    
    # Port mapping (internal only)
    expose:
      - "6379"
    
    # Volume for persistence
    volumes:
      - redis-data:/data
    
    # Health check
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    
    # Security settings
    security_opt:
      - no-new-privileges:true
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"
        labels: "service=redis,environment=production"
    
    # Networks
    networks:
      - carnow-network

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: carnow-nginx-prod
    restart: unless-stopped
    
    # Port mapping
    ports:
      - "80:80"
      - "443:443"
    
    # Configuration
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    
    # Health check
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 128M
        reservations:
          cpus: '0.25'
          memory: 64M
    
    # Security settings
    security_opt:
      - no-new-privileges:true
    
    # Dependencies
    depends_on:
      carnow-backend:
        condition: service_healthy
    
    # Networks
    networks:
      - carnow-network

  # Prometheus Monitoring (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: carnow-prometheus-prod
    restart: unless-stopped
    
    # Configuration
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    
    # Port mapping (internal only)
    expose:
      - "9090"
    
    # Command
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    
    # Networks
    networks:
      - carnow-network

  # Grafana Dashboard (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: carnow-grafana-prod
    restart: unless-stopped
    
    # Environment variables
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    
    # Port mapping (internal only)
    expose:
      - "3000"
    
    # Volumes
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    
    # Dependencies
    depends_on:
      - prometheus
    
    # Networks
    networks:
      - carnow-network

# Networks
networks:
  carnow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  nginx-logs:
    driver: local

# Secrets (for Docker Swarm)
secrets:
  supabase_anon_key:
    external: true
  supabase_service_role_key:
    external: true
  jwt_secret:
    external: true
  google_client_secret:
    external: true
